#!/usr/bin/env python3
"""
测试网络连接和SSE端点
"""

import requests
import json

def test_sse_endpoint():
    """测试SSE端点"""
    sse_url = "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"
    
    print(f"🔍 测试SSE端点: {sse_url}")
    
    # 测试基本连接
    try:
        print("📡 测试基本连接...")
        response = requests.get(sse_url, timeout=10)
        print(f"✅ 基本连接成功，状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        if response.text:
            print(f"📄 响应内容前200字符: {response.text[:200]}")
    except Exception as e:
        print(f"❌ 基本连接失败: {str(e)}")
    
    # 测试工具列表端点
    endpoints_to_test = [
        sse_url.replace('/sse', '/tools'),
        sse_url.replace('/sse', ''),
        sse_url + '/tools',
        sse_url.replace('/sse', '/api/tools'),
        sse_url.replace('/sse', '/mcp/tools'),
    ]
    
    request_data = {
        "jsonrpc": "2.0",
        "id": 1,
        "method": "tools/list",
        "params": {}
    }
    
    for endpoint in endpoints_to_test:
        print(f"\n🔗 测试端点: {endpoint}")
        
        # 测试GET请求
        try:
            response = requests.get(endpoint, timeout=10)
            print(f"  GET - 状态码: {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  GET - JSON响应: {data}")
                except:
                    print(f"  GET - 文本响应: {response.text[:200]}")
        except Exception as e:
            print(f"  GET - 失败: {str(e)}")
        
        # 测试POST请求
        try:
            response = requests.post(
                endpoint, 
                json=request_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            print(f"  POST - 状态码: {response.status_code}")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"  POST - JSON响应: {data}")
                except:
                    print(f"  POST - 文本响应: {response.text[:200]}")
        except Exception as e:
            print(f"  POST - 失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始网络连接测试")
    test_sse_endpoint()
    print("🎉 测试完成")
