#!/usr/bin/env python3
"""
测试缓存工具的MCP客户端
"""

import asyncio
import json
from test import MC<PERSON>lient

async def test_cached_tools():
    """测试缓存工具功能"""
    print("🧪 测试缓存工具功能...")
    
    # 模拟你的配置
    mcp_config = {
        "mcpServers": {
            "charts": {
                "command": "uvx",
                "args": ["mcp-server-charts"],
                "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"  # 备用SSE
            }
        }
    }
    
    client = MCPClient(mcp_config)
    
    try:
        print("🔗 连接到服务器...")
        await client.connect_to_server("charts")
        
        if client.is_connected():
            print("✅ 连接成功！")
            print(f"📋 服务器类型: {client.server_type}")
            
            # 第一次获取工具（应该使用缓存）
            print("\n🔧 第一次获取工具...")
            tools1 = await client.get_available_tools()
            if tools1:
                print(f"✅ 获取到 {len(tools1)} 个工具")
                print(f"🔧 工具列表: {[tool['function']['name'] for tool in tools1[:5]]}...")
            else:
                print("❌ 未获取到工具")
            
            # 第二次获取工具（应该直接使用缓存，避免异步问题）
            print("\n🔧 第二次获取工具（测试缓存）...")
            tools2 = await client.get_available_tools()
            if tools2:
                print(f"✅ 缓存获取到 {len(tools2)} 个工具")
                print(f"🔧 工具列表: {[tool['function']['name'] for tool in tools2[:5]]}...")
                
                # 验证两次获取的结果是否一致
                if len(tools1) == len(tools2):
                    print("✅ 缓存工具数量一致")
                else:
                    print("⚠️ 缓存工具数量不一致")
            else:
                print("❌ 缓存获取失败")
            
            # 测试工具调用
            if tools1:
                first_tool = tools1[0]['function']
                tool_name = first_tool['name']
                print(f"\n🧪 测试调用工具: {tool_name}")
                
                try:
                    # 构建测试参数
                    if 'chart' in tool_name:
                        test_args = {
                            "data": [{"name": "A", "value": 10}, {"name": "B", "value": 20}],
                            "title": "测试图表"
                        }
                    else:
                        test_args = {}
                    
                    result = await client.call_tool(tool_name, test_args)
                    print(f"✅ 工具调用成功: {result[:100]}...")
                except Exception as e:
                    print(f"❌ 工具调用失败: {str(e)}")
        else:
            print("❌ 连接失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🧹 清理资源...")
        await client.cleanup()
        print("✅ 测试完成")

def test_tool_caching_info():
    """显示工具缓存机制说明"""
    print("\n📋 工具缓存机制说明:")
    print("=" * 50)
    print("1. 连接时自动缓存工具列表")
    print("2. 后续调用优先使用缓存")
    print("3. 避免重复的异步工具获取")
    print("4. 如果缓存失败，尝试重新获取")
    print("5. 清理时自动清除缓存")
    print("=" * 50)

if __name__ == "__main__":
    test_tool_caching_info()
    asyncio.run(test_cached_tools())
