#!/usr/bin/env python3
"""
简化的MCP工具测试
"""

import asyncio
import json
import requests
from typing import Optional, Dict, Any

class SimpleMCPClient:
    def __init__(self, mcp_config: Dict[str, Any]):
        self.mcp_config = mcp_config
        self.server_type = None
        self.server_name = None
        self.sse_url = None
        self._connected = False

    async def connect_to_server(self, server_name: str):
        self.server_name = server_name
        server_config = self.mcp_config['mcpServers'][server_name]
        
        if 'type' in server_config and server_config['type'] == 'sse':
            self.server_type = 'sse'
            self.sse_url = server_config['url']
            
            print(f"🔗 正在连接到SSE服务器: {server_config['url']}")
            
            # 测试连接
            try:
                test_url = self.sse_url.replace('/sse', '/ping') if '/sse' in self.sse_url else self.sse_url + '/ping'
                response = requests.get(test_url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ SSE服务器连接测试成功")
                else:
                    print(f"⚠️ SSE服务器响应状态码: {response.status_code}")
            except requests.RequestException:
                print(f"⚠️ SSE服务器ping测试失败，但继续尝试连接")
            
            self._connected = True
            print(f"✅ 已连接到SSE服务器: {server_config['url']}")
            
            # 立即尝试获取工具列表
            tools = await self.get_available_tools()
            if tools:
                print(f"🔧 成功获取到 {len(tools)} 个工具")
            else:
                print("⚠️ 未能获取到工具列表，但连接已建立")
        else:
            self.server_type = 'fastmcp'
            self._connected = True
            print(f"✅ 已连接到FastMCP服务器: '{server_name}'")

    def is_connected(self) -> bool:
        return self._connected

    async def get_available_tools(self):
        """Get list of available MCP tools"""
        if not self.is_connected():
            print("❌ MCP客户端未连接")
            return []

        if self.server_type == 'sse':
            try:
                sse_url = self.sse_url
                if not sse_url:
                    print("❌ SSE服务器URL未设置")
                    return []

                print(f"🔍 尝试从SSE服务器获取工具列表: {sse_url}")
                
                # 构建标准的MCP工具列表请求
                request_data = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "tools/list",
                    "params": {}
                }

                # 尝试不同的端点
                endpoints_to_try = [
                    sse_url.replace('/sse', '/tools'),  # 替换sse为tools
                    sse_url.replace('/sse', ''),  # 移除sse后缀
                    sse_url + '/tools',  # 添加tools路径
                    sse_url.replace('/sse', '/api/tools'),  # API前缀
                ]

                for endpoint in endpoints_to_try:
                    try:
                        print(f"🔗 尝试端点: {endpoint}")
                        
                        # 尝试POST请求
                        response = requests.post(
                            endpoint, 
                            json=request_data, 
                            headers={'Content-Type': 'application/json'},
                            timeout=10
                        )
                        
                        print(f"📡 响应状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            try:
                                result = response.json()
                                print(f"📋 响应内容: {result}")
                                
                                # 解析工具列表
                                tools_data = None
                                if 'result' in result and 'tools' in result['result']:
                                    tools_data = result['result']['tools']
                                elif 'tools' in result:
                                    tools_data = result['tools']
                                
                                if tools_data:
                                    tools = []
                                    for tool in tools_data:
                                        tool_def = {
                                            "type": "function",
                                            "function": {
                                                "name": tool.get("name", "unknown"),
                                                "description": tool.get("description", "No description"),
                                                "parameters": tool.get("inputSchema", {
                                                    "type": "object",
                                                    "properties": {},
                                                    "required": []
                                                })
                                            }
                                        }
                                        tools.append(tool_def)
                                    
                                    tool_names = [tool['function']['name'] for tool in tools]
                                    print(f"✅ SSE工具加载成功: {tool_names}")
                                    return tools
                                    
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON解析失败: {str(e)}")
                                continue
                        else:
                            print(f"❌ 请求失败: {response.status_code} - {response.text[:200]}")
                            
                    except requests.RequestException as e:
                        print(f"❌ 请求异常: {str(e)}")
                        continue

                # 如果所有端点都失败，返回默认工具
                print("⚠️ 无法从SSE服务器获取工具列表，使用默认工具")
                default_tools = [
                    {
                        "type": "function",
                        "function": {
                            "name": "fetch_url",
                            "description": "Fetch content from a URL",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "url": {
                                        "type": "string",
                                        "description": "The URL to fetch"
                                    }
                                },
                                "required": ["url"]
                            }
                        }
                    }
                ]
                tool_names = [tool['function']['name'] for tool in default_tools]
                print(f"📦 默认工具已加载: {tool_names}")
                return default_tools
                
            except Exception as e:
                print(f"❌ 获取SSE工具时发生错误: {str(e)}")
                return []
        else:
            print("❌ 未知的服务器类型")
            return []

async def test_sse_connection():
    """测试SSE连接和工具获取"""
    print("🧪 开始测试SSE连接...")
    
    # 你的配置
    mcp_config = {
        "mcpServers": {
            "fetch": {
                "type": "sse",
                "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"
            }
        }
    }
    
    client = SimpleMCPClient(mcp_config)
    
    try:
        await client.connect_to_server("fetch")
        
        if client.is_connected():
            print("✅ 连接成功！")
            
            tools = await client.get_available_tools()
            
            if tools:
                print(f"\n🎉 成功获取到 {len(tools)} 个工具:")
                for i, tool in enumerate(tools, 1):
                    tool_info = tool['function']
                    print(f"  {i}. {tool_info['name']}: {tool_info['description']}")
            else:
                print("⚠️ 未获取到任何工具")
        else:
            print("❌ 连接失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_sse_connection())
