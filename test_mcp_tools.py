#!/usr/bin/env python3
"""
测试MCP工具加载的脚本
"""

import asyncio
import json
from test import MCPClient

async def test_sse_tools():
    """测试SSE服务器的工具加载"""
    print("🧪 开始测试SSE工具加载...")
    
    # 模拟你的配置
    mcp_config = {
        "mcpServers": {
            "fetch": {
                "type": "sse",
                "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"
            }
        }
    }
    
    # 创建MCP客户端
    client = MCPClient(mcp_config)
    
    try:
        # 连接到服务器
        print("🔗 正在连接到SSE服务器...")
        await client.connect_to_server("fetch")
        
        if client.is_connected():
            print("✅ 连接成功！")
            
            # 获取工具列表
            print("🔧 正在获取工具列表...")
            tools = await client.get_available_tools()
            
            if tools:
                print(f"✅ 成功获取到 {len(tools)} 个工具:")
                for i, tool in enumerate(tools, 1):
                    tool_info = tool['function']
                    print(f"  {i}. {tool_info['name']}: {tool_info['description']}")
                    
                # 测试工具调用
                if tools:
                    first_tool = tools[0]['function']
                    tool_name = first_tool['name']
                    print(f"\n🧪 测试调用工具: {tool_name}")
                    
                    # 根据工具类型构建测试参数
                    if tool_name == "fetch_url":
                        test_args = {"url": "https://httpbin.org/json"}
                    elif tool_name == "search_web":
                        test_args = {"query": "test query"}
                    else:
                        # 使用工具的第一个必需参数
                        required_params = first_tool.get('parameters', {}).get('required', [])
                        if required_params:
                            test_args = {required_params[0]: "test_value"}
                        else:
                            test_args = {}
                    
                    try:
                        result = await client.call_tool(tool_name, test_args)
                        print(f"✅ 工具调用成功: {result}")
                    except Exception as e:
                        print(f"❌ 工具调用失败: {str(e)}")
            else:
                print("⚠️ 未获取到任何工具")
        else:
            print("❌ 连接失败")
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
    finally:
        # 清理资源
        await client.cleanup()
        print("🧹 资源清理完成")

async def test_fastmcp_tools():
    """测试FastMCP的工具加载"""
    print("\n🧪 开始测试FastMCP工具加载...")
    
    # FastMCP配置
    mcp_config = {
        "mcpServers": {
            "fastmcp": {
                "type": "fastmcp"
            }
        }
    }
    
    client = MCPClient(mcp_config)
    
    try:
        await client.connect_to_server("fastmcp")
        
        if client.is_connected():
            print("✅ FastMCP连接成功！")
            
            tools = await client.get_available_tools()
            
            if tools:
                print(f"✅ FastMCP成功获取到 {len(tools)} 个工具:")
                for i, tool in enumerate(tools, 1):
                    tool_info = tool['function']
                    print(f"  {i}. {tool_info['name']}: {tool_info['description']}")
            else:
                print("⚠️ FastMCP未获取到任何工具")
        else:
            print("❌ FastMCP连接失败")
            
    except Exception as e:
        print(f"❌ FastMCP测试过程中发生错误: {str(e)}")
    finally:
        await client.cleanup()

def print_config_example():
    """打印配置示例"""
    print("\n📋 配置示例:")
    print("=" * 50)
    
    example_config = {
        "mcpServers": {
            "fetch": {
                "type": "sse",
                "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"
            }
        }
    }
    
    print(json.dumps(example_config, indent=2, ensure_ascii=False))
    print("=" * 50)

async def main():
    """主测试函数"""
    print("🚀 MCP工具加载测试开始")
    print_config_example()
    
    # 测试SSE工具
    await test_sse_tools()
    
    # 测试FastMCP工具
    await test_fastmcp_tools()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
