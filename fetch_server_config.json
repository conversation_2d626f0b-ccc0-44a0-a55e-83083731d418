{"recommended_sse_only": {"mcpServers": {"fetch": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"}}}, "stdio_with_sse_fallback": {"mcpServers": {"fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"}}}, "pure_sse_config": {"mcpServers": {"fetch": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse", "description": "ModelScope MCP Fetch Service"}}}}