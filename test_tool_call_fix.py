#!/usr/bin/env python3
"""
测试工具调用修复
"""

import asyncio
import json
from test import MCPClient

async def test_tool_calling():
    """测试工具调用功能"""
    print("🧪 测试工具调用修复...")
    
    # 模拟你的配置
    mcp_config = {
        "mcpServers": {
            "charts": {
                "command": "uvx",
                "args": ["mcp-server-charts"],
                "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"  # 备用SSE
            }
        }
    }
    
    client = MCPClient(mcp_config)
    
    try:
        print("🔗 连接到服务器...")
        await client.connect_to_server("charts")
        
        if client.is_connected():
            print("✅ 连接成功！")
            
            # 获取工具列表
            tools = await client.get_available_tools()
            if tools:
                print(f"🔧 获取到 {len(tools)} 个工具")
                
                # 测试异步工具调用
                print("\n🧪 测试异步工具调用...")
                try:
                    test_args = {
                        "data": [
                            {"name": "北京", "value": 100},
                            {"name": "上海", "value": 80},
                            {"name": "广州", "value": 60}
                        ],
                        "title": "城市销售数据"
                    }
                    
                    result = await client.call_tool("generate_column_chart", test_args)
                    print(f"✅ 异步调用成功: {result[:100]}...")
                except Exception as e:
                    print(f"❌ 异步调用失败: {str(e)}")
                
                # 测试同步工具调用
                print("\n🧪 测试同步工具调用...")
                try:
                    test_args = {
                        "data": [
                            {"name": "产品A", "value": 30},
                            {"name": "产品B", "value": 45},
                            {"name": "产品C", "value": 25}
                        ],
                        "title": "产品销售占比"
                    }
                    
                    result = client._sync_call_tool("generate_pie_chart", test_args)
                    print(f"✅ 同步调用成功: {result[:100]}...")
                except Exception as e:
                    print(f"❌ 同步调用失败: {str(e)}")
                
                # 测试多个工具调用
                print("\n🧪 测试多个工具调用...")
                test_tools = [
                    ("generate_bar_chart", {"data": [{"name": "Q1", "value": 100}], "title": "季度数据"}),
                    ("generate_line_chart", {"data": [{"name": "1月", "value": 50}], "title": "月度趋势"}),
                    ("generate_area_chart", {"data": [{"name": "区域A", "value": 75}], "title": "区域分析"})
                ]
                
                for tool_name, args in test_tools:
                    try:
                        result = client._sync_call_tool(tool_name, args)
                        print(f"✅ {tool_name} 调用成功")
                    except Exception as e:
                        print(f"❌ {tool_name} 调用失败: {str(e)}")
            else:
                print("❌ 未获取到工具")
        else:
            print("❌ 连接失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🧹 清理资源...")
        await client.cleanup()
        print("✅ 测试完成")

def print_fix_summary():
    """打印修复总结"""
    print("\n📋 工具调用修复总结:")
    print("=" * 50)
    print("🔧 问题: asyncio.run()在现有事件循环中调用工具导致冲突")
    print("✅ 解决: 添加_sync_call_tool()方法使用独立事件循环")
    print("🔄 机制: 新事件循环 -> 调用工具 -> 关闭循环")
    print("🛡️ 安全: 避免与stdio连接的事件循环冲突")
    print("📈 效果: 工具调用稳定可靠，无异步错误")
    print("=" * 50)

if __name__ == "__main__":
    print_fix_summary()
    asyncio.run(test_tool_calling())
