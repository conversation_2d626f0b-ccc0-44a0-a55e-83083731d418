#!/usr/bin/env python3
"""
最终修复验证测试
"""

import asyncio
import json
from test import MCPClient

async def test_final_fix():
    """测试最终修复"""
    print("🧪 测试最终修复...")
    
    # 模拟你的配置
    mcp_config = {
        "mcpServers": {
            "charts": {
                "command": "uvx",
                "args": ["mcp-server-charts"],
                "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"  # 备用SSE
            }
        }
    }
    
    client = MCPClient(mcp_config)
    
    try:
        print("🔗 连接到服务器...")
        await client.connect_to_server("charts")
        
        if client.is_connected():
            print("✅ 连接成功！")
            print(f"📋 服务器类型: {client.server_type}")
            
            # 测试工具获取
            print("\n🔧 测试工具获取...")
            tools = await client.get_available_tools()
            if tools:
                print(f"✅ 获取到 {len(tools)} 个工具")
                print(f"🔧 前5个工具: {[tool['function']['name'] for tool in tools[:5]]}")
            else:
                print("❌ 未获取到工具")
                return
            
            # 测试同步工具调用
            print("\n🧪 测试同步工具调用...")
            test_cases = [
                {
                    "tool": "generate_column_chart",
                    "args": {
                        "data": [
                            {"name": "北京", "value": 100},
                            {"name": "上海", "value": 80},
                            {"name": "广州", "value": 60}
                        ],
                        "title": "城市销售数据"
                    }
                },
                {
                    "tool": "generate_pie_chart", 
                    "args": {
                        "data": [
                            {"name": "产品A", "value": 30},
                            {"name": "产品B", "value": 45},
                            {"name": "产品C", "value": 25}
                        ],
                        "title": "产品销售占比"
                    }
                }
            ]
            
            for i, test_case in enumerate(test_cases, 1):
                tool_name = test_case["tool"]
                args = test_case["args"]
                
                print(f"\n📊 测试 {i}: {tool_name}")
                try:
                    result = client._sync_call_tool(tool_name, args)
                    print(f"✅ 调用成功: {result[:100]}...")
                except Exception as e:
                    print(f"❌ 调用失败: {str(e)}")
            
            print("\n🎉 所有测试完成！")
        else:
            print("❌ 连接失败")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🧹 清理资源...")
        await client.cleanup()
        print("✅ 清理完成")

def print_final_summary():
    """打印最终修复总结"""
    print("\n📋 最终修复总结:")
    print("=" * 60)
    print("🔧 问题1: 工具调用方法名错误")
    print("   修复: self._sync_call_tool -> self.mcp_client._sync_call_tool")
    print()
    print("🔧 问题2: AsyncExitStack异步清理冲突")
    print("   修复: 直接使用stdio_client生成器，避免AsyncExitStack")
    print()
    print("🔧 问题3: 事件循环冲突")
    print("   修复: _sync_call_tool使用独立事件循环")
    print()
    print("🔧 问题4: 资源清理不完整")
    print("   修复: 分步清理会话、流、生成器")
    print()
    print("✅ 预期效果:")
    print("   - 工具调用成功")
    print("   - 异步警告减少（可能仍有少量警告但不影响功能）")
    print("   - 连接稳定可靠")
    print("=" * 60)

if __name__ == "__main__":
    print_final_summary()
    asyncio.run(test_final_fix())
