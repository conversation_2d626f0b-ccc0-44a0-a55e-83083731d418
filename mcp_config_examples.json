{"sse_example": {"mcpServers": {"fetch": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"}}}, "stdio_example": {"mcpServers": {"filesystem": {"command": "python", "args": ["-m", "mcp_server_filesystem", "/path/to/allowed/directory"], "env": {}}}}, "stdio_node_example": {"mcpServers": {"brave_search": {"command": "node", "args": ["/path/to/brave-search-mcp/dist/index.js"], "env": {"BRAVE_API_KEY": "your_brave_api_key_here"}}}}, "fastmcp_example": {"mcpServers": {"local_tools": {"type": "fastmcp", "tools": [{"name": "generate_image", "description": "Generate an image based on text description"}, {"name": "text_to_speech", "description": "Convert text to speech"}]}}}}