import os
import sys
import json
import time
import tempfile
import gradio as gr
import requests
import asyncio
from PIL import Image as PILImage
import io
from mcpserver import FastMCP
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from openai import OpenAI
from dotenv import load_dotenv
import atexit
from sseclient import SSEClient
from typing import Optional, Dict, Any
from contextlib import AsyncExitStack

# Load environment variables
load_dotenv()

class MCPClient:
    def __init__(self, mcp_config: Dict[str, Any]):
        # Initialize session and client objects
        self.session: Optional[ClientSession] = None
        self.mcp_config = mcp_config
        self.server_type = None
        self.server_name = None
        self.sse_client = None
        self._connected = False
        self.exit_stack = None  # 延迟初始化
        self.stdio = None
        self.write = None
        self.sse_url = None
        self._stdio_process = None  # 保存子进程引用

    async def connect_to_server(self, server_name: str):
        """Connect to an MCP server (stdio, SSE, or FastMCP)"""
        self.server_name = server_name
        server_config = self.mcp_config['mcpServers'][server_name]

        if 'type' in server_config and server_config['type'] == 'sse':
            # SSE连接
            self.server_type = 'sse'
            try:
                self.sse_url = server_config['url']
                print(f"🔗 正在连接到SSE服务器: {server_config['url']}")

                # 测试SSE连接
                import requests
                test_url = self.sse_url.replace('/sse', '/ping') if '/sse' in self.sse_url else self.sse_url + '/ping'

                try:
                    response = requests.get(test_url, timeout=5)
                    if response.status_code == 200:
                        print(f"✅ SSE服务器连接测试成功")
                    else:
                        print(f"⚠️ SSE服务器响应状态码: {response.status_code}")
                except requests.RequestException:
                    print(f"⚠️ SSE服务器ping测试失败，但继续尝试连接")

                self._connected = True
                print(f"✅ 已连接到SSE服务器: {server_config['url']}")

                # 立即尝试获取工具列表来验证连接
                tools = await self.get_available_tools()
                if tools:
                    print(f"🔧 成功获取到 {len(tools)} 个工具")
                else:
                    print("⚠️ 未能获取到工具列表，但连接已建立")

            except Exception as e:
                print(f"❌ 连接SSE服务器失败: {str(e)}")
                self.sse_url = None
                self._connected = False

        elif 'command' in server_config:
            # Stdio连接 (重新设计，避免异步资源管理问题)
            self.server_type = 'stdio'
            try:
                print(f"🔗 正在连接到Stdio服务器: {server_config}")

                # 检查命令是否存在
                import shutil
                command = server_config['command']
                if not shutil.which(command):
                    print(f"❌ 命令 '{command}' 未找到，尝试回退到SSE连接")
                    # 尝试回退到SSE连接
                    if 'url' in server_config:
                        print(f"🔄 回退到SSE连接: {server_config['url']}")
                        self.server_type = 'sse'
                        self.sse_url = server_config['url']
                        self._connected = True
                        print(f"✅ 已回退到SSE服务器: {server_config['url']}")

                        # 立即尝试获取工具列表
                        tools = await self.get_available_tools()
                        if tools:
                            print(f"🔧 成功获取到 {len(tools)} 个工具")
                        else:
                            print("⚠️ 未能获取到工具列表，但连接已建立")
                        return
                    else:
                        raise FileNotFoundError(f"命令 '{command}' 未找到，且没有备用SSE URL")

                # 使用简化的连接方式，避免AsyncExitStack的问题
                from contextlib import AsyncExitStack
                self.exit_stack = AsyncExitStack()

                # 构建服务器参数
                server_params = StdioServerParameters(
                    command=server_config['command'],
                    args=server_config.get('args', []),
                    env=server_config.get('env', None)
                )

                # 建立stdio连接
                stdio_transport = await self.exit_stack.enter_async_context(stdio_client(server_params))
                self.stdio, self.write = stdio_transport
                self.session = await self.exit_stack.enter_async_context(ClientSession(self.stdio, self.write))

                # 初始化会话
                await self.session.initialize()

                # 获取工具列表
                response = await self.session.list_tools()
                tools = response.tools
                print(f"✅ 已连接到Stdio服务器，可用工具: {[tool.name for tool in tools]}")

                self._connected = True

            except Exception as e:
                print(f"❌ 连接Stdio服务器失败: {str(e)}")

                # 清理失败的连接
                if self.exit_stack:
                    try:
                        await self.exit_stack.aclose()
                    except:
                        pass
                    self.exit_stack = None

                # 尝试回退到SSE连接
                if 'url' in server_config:
                    print(f"🔄 尝试回退到SSE连接: {server_config['url']}")
                    try:
                        self.server_type = 'sse'
                        self.sse_url = server_config['url']
                        self._connected = True
                        print(f"✅ 已回退到SSE服务器: {server_config['url']}")

                        # 立即尝试获取工具列表
                        tools = await self.get_available_tools()
                        if tools:
                            print(f"🔧 回退SSE成功获取到 {len(tools)} 个工具")
                        else:
                            print("⚠️ 回退SSE未能获取到工具列表，但连接已建立")
                    except Exception as sse_e:
                        print(f"❌ SSE回退也失败: {str(sse_e)}")
                        self._connected = False
                else:
                    self._connected = False
        else:
            # FastMCP服务器
            self.server_type = 'fastmcp'
            self._connected = True
            print(f"✅ 已连接到FastMCP服务器: '{server_name}'")

    async def process_query(self, query: str, openai_client=None, model="gpt-4o") -> str:
        """Process a query using OpenAI and available MCP tools (based on official example)"""
        if not self._connected:
            raise RuntimeError("Not connected to MCP server")

        if not openai_client:
            # 如果没有提供OpenAI客户端，返回简单响应
            return f"查询已处理: {query}"

        messages = [
            {
                "role": "user",
                "content": query
            }
        ]

        try:
            # 获取可用工具
            available_tools = await self.get_available_tools()

            # 初始OpenAI API调用
            openai_response = openai_client.chat.completions.create(
                model=model,
                messages=messages,
                tools=available_tools if available_tools else None,
                tool_choice="auto"
            )

            print(f"🤖 OpenAI响应: {openai_response}")

            # 处理响应和工具调用
            final_text = []
            message = openai_response.choices[0].message

            if message.content:
                final_text.append(message.content)
                messages.append(message)

            if message.tool_calls:
                messages.append(message)
                for tool_call in message.tool_calls:
                    tool_name = tool_call.function.name
                    tool_args = tool_call.function.arguments

                    try:
                        parsed_args = json.loads(tool_args)
                    except json.JSONDecodeError:
                        parsed_args = tool_args

                    # 执行工具调用
                    result = await self.call_tool(tool_name, parsed_args)
                    final_text.append(f"[调用工具 {tool_name} 参数 {tool_args}]")

                    messages.append({
                        "tool_call_id": tool_call.id,
                        "role": "tool",
                        "name": tool_name,
                        "content": str(result)
                    })

                    # 获取工具执行后的OpenAI响应
                    openai_response = openai_client.chat.completions.create(
                        model=model,
                        messages=messages,
                        tools=available_tools if available_tools else None,
                        tool_choice="auto"
                    )

                    if openai_response.choices[0].message.content:
                        final_text.append(openai_response.choices[0].message.content)
                        messages.append(openai_response.choices[0].message)

            return "\n".join(final_text)

        except Exception as e:
            print(f"❌ 处理查询时出错: {str(e)}")
            if self.server_type == 'fastmcp':
                return f"FastMCP服务器已处理查询: {query}"
            elif self.server_type == 'sse':
                return f"查询已发送到SSE服务器: {query}"
            else:
                return f"查询处理失败: {str(e)}"

    async def cleanup(self):
        """Clean up resources - 重新设计以避免异步问题"""
        print("🧹 开始清理MCP客户端资源...")

        if self._connected:
            try:
                if self.server_type == 'stdio':
                    # 安全清理stdio连接
                    print("🔄 清理Stdio连接...")

                    # 首先尝试正常关闭会话
                    if self.session:
                        try:
                            # 不直接关闭session，让exit_stack处理
                            self.session = None
                            print("  ✅ 会话引用已清理")
                        except Exception as e:
                            print(f"  ⚠️ 会话清理警告: {str(e)}")

                    # 清理exit_stack
                    if self.exit_stack:
                        try:
                            # 在同一个任务中清理
                            await self.exit_stack.aclose()
                            print("  ✅ Exit stack已清理")
                        except Exception as e:
                            print(f"  ⚠️ Exit stack清理警告: {str(e)}")
                            # 如果正常清理失败，强制清理
                            try:
                                self.exit_stack = None
                                print("  ✅ 强制清理exit stack")
                            except:
                                pass

                    print("✅ Stdio连接已安全关闭")

                elif self.server_type == 'sse':
                    if self.sse_client and hasattr(self.sse_client, 'close'):
                        try:
                            self.sse_client.close()
                            print("✅ SSE连接已关闭")
                        except Exception as e:
                            print(f"⚠️ SSE关闭警告: {str(e)}")
                    else:
                        print("✅ SSE连接已断开")

                elif self.server_type == 'fastmcp':
                    print("✅ FastMCP连接已断开")

            except Exception as e:
                print(f"❌ 清理连接时出错: {str(e)}")
                # 强制清理所有引用
                try:
                    self.session = None
                    self.exit_stack = None
                    self.sse_client = None
                    print("✅ 强制清理完成")
                except:
                    pass
            finally:
                # 重置所有状态
                self._connected = False
                self.session = None
                self.sse_client = None
                self.stdio = None
                self.write = None
                self.exit_stack = None
                print("✅ MCP客户端资源清理完成")

    def is_connected(self) -> bool:
        """Check if the client is properly connected"""
        return self._connected and (
            self.server_type == 'fastmcp' or
            (self.server_type == 'sse' and self.sse_url is not None) or
            (self.server_type == 'stdio' and self.session is not None)
        )

    def _parse_tools_response(self, result):
        """解析工具响应，提取工具数据"""
        tools_data = None

        # 尝试不同的响应格式
        if isinstance(result, dict):
            # JSON-RPC格式: {"result": {"tools": [...]}}
            if 'result' in result and isinstance(result['result'], dict) and 'tools' in result['result']:
                tools_data = result['result']['tools']
            # 直接格式: {"tools": [...]}
            elif 'tools' in result:
                tools_data = result['tools']
            # 数组格式: [tool1, tool2, ...]
            elif isinstance(result, list):
                tools_data = result
        elif isinstance(result, list):
            # 直接是工具数组
            tools_data = result

        return tools_data if tools_data else None

    def _format_tools(self, tools_data):
        """格式化工具数据为标准格式"""
        tools = []
        for tool in tools_data:
            if isinstance(tool, dict):
                tool_def = {
                    "type": "function",
                    "function": {
                        "name": tool.get("name", "unknown"),
                        "description": tool.get("description", "No description"),
                        "parameters": tool.get("inputSchema", tool.get("parameters", {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }))
                    }
                }
                tools.append(tool_def)
        return tools

    async def _probe_sse_endpoints(self, sse_url):
        """探测SSE服务器的可用端点"""
        import requests

        # 可能的端点路径
        probe_paths = [
            '',  # 根路径
            '/tools',
            '/api/tools',
            '/mcp/tools',
            '/list',
            '/api/list',
            '/mcp/list',
            '/capabilities',
            '/api/capabilities',
        ]

        available_endpoints = []
        base_url = sse_url.replace('/sse', '')

        for path in probe_paths:
            endpoint = base_url + path
            try:
                # 快速探测，短超时
                response = requests.get(endpoint, timeout=3)
                if response.status_code in [200, 404, 405]:  # 404和405也表示端点存在
                    available_endpoints.append(endpoint)
                    print(f"  ✅ 发现端点: {endpoint} (状态码: {response.status_code})")
            except:
                # 忽略连接错误
                pass

        return available_endpoints

    async def get_available_tools(self):
        """Get list of available MCP tools for the LLM"""
        if not self.is_connected():
            print("❌ MCP客户端未连接")
            return []

        if self.server_type == 'fastmcp':
            # 返回FastMCP的默认工具
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "generate_image",
                        "description": "Generate an image based on text description",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "prompt": {
                                    "type": "string",
                                    "description": "Text description of the image to generate"
                                }
                            },
                            "required": ["prompt"]
                        }
                    }
                },
                {
                    "type": "function",
                    "function": {
                        "name": "text_to_speech",
                        "description": "Convert text to speech",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "text": {
                                    "type": "string",
                                    "description": "Text to convert to speech"
                                }
                            },
                            "required": ["text"]
                        }
                    }
                }
            ]
            print(f"✅ FastMCP工具加载成功: {[tool['function']['name'] for tool in tools]}")
            return tools

        elif self.server_type == 'stdio':
            try:
                if not self.session:
                    print("❌ Stdio会话未初始化")
                    return []

                response = await self.session.list_tools()
                tools = [{
                    "type": "function",
                    "function": {
                        "name": tool.name,
                        "description": tool.description,
                        "parameters": tool.inputSchema  # 直接使用JSON Schema
                    }
                } for tool in response.tools]
                print(f"✅ Stdio工具加载成功: {[tool['function']['name'] for tool in tools]}")
                return tools
            except Exception as e:
                print(f"❌ 获取Stdio工具失败: {str(e)}")
                return []

        elif self.server_type == 'sse':
            # 对于SSE服务器，尝试获取工具列表
            try:
                import requests
                import json

                sse_url = getattr(self, 'sse_url', None)
                if not sse_url:
                    print("❌ SSE服务器URL未设置")
                    return []

                print(f"🔍 尝试从SSE服务器获取工具列表: {sse_url}")

                # 首先尝试探测服务器支持的端点
                available_endpoints = await self._probe_sse_endpoints(sse_url)
                print(f"🔍 探测到的可用端点: {available_endpoints}")

                # 构建标准的MCP工具列表请求
                request_data = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "tools/list",
                    "params": {}
                }

                # 使用探测到的端点，如果没有则使用默认端点
                if available_endpoints:
                    endpoints_to_try = available_endpoints
                else:
                    # 默认端点列表
                    endpoints_to_try = [
                        sse_url.replace('/sse', '/tools'),  # 替换sse为tools
                        sse_url.replace('/sse', ''),  # 移除sse后缀
                        sse_url + '/tools',  # 添加tools路径
                        sse_url.replace('/sse', '/api/tools'),  # API前缀
                        sse_url.replace('/sse', '/mcp/tools'),  # MCP前缀
                        sse_url,  # 原始URL
                    ]

                # 尝试不同的请求格式
                request_formats = [
                    # 标准MCP JSON-RPC格式
                    {
                        "jsonrpc": "2.0",
                        "id": 1,
                        "method": "tools/list",
                        "params": {}
                    },
                    # 简化格式
                    {
                        "method": "tools/list"
                    },
                    # 直接方法名
                    {
                        "method": "list_tools"
                    },
                    # 空请求体
                    {}
                ]

                for endpoint in endpoints_to_try:
                    print(f"🔗 尝试端点: {endpoint}")

                    # 首先尝试GET请求
                    try:
                        response = requests.get(endpoint, timeout=10)
                        print(f"📡 GET响应状态码: {response.status_code}")

                        if response.status_code == 200:
                            try:
                                result = response.json()
                                print(f"📋 GET响应内容: {result}")

                                # 解析工具列表
                                tools_data = self._parse_tools_response(result)
                                if tools_data:
                                    tools = self._format_tools(tools_data)
                                    tool_names = [tool['function']['name'] for tool in tools]
                                    print(f"✅ SSE工具加载成功(GET): {tool_names}")
                                    return tools

                            except json.JSONDecodeError as e:
                                print(f"❌ GET JSON解析失败: {str(e)}")
                            except Exception as e:
                                print(f"❌ GET处理失败: {str(e)}")
                        else:
                            print(f"⚠️ GET请求失败: {response.status_code}")

                    except requests.RequestException as e:
                        print(f"⚠️ GET请求异常: {str(e)}")

                    # 然后尝试POST请求，使用不同的请求格式
                    for request_data in request_formats:
                        try:
                            print(f"  📤 尝试POST请求格式: {request_data}")
                            response = requests.post(
                                endpoint,
                                json=request_data,
                                headers={'Content-Type': 'application/json'},
                                timeout=10
                            )

                            print(f"  📡 POST响应状态码: {response.status_code}")

                            if response.status_code == 200:
                                try:
                                    result = response.json()
                                    print(f"  📋 POST响应内容: {result}")

                                    # 解析工具列表
                                    tools_data = self._parse_tools_response(result)
                                    if tools_data:
                                        tools = self._format_tools(tools_data)
                                        tool_names = [tool['function']['name'] for tool in tools]
                                        print(f"✅ SSE工具加载成功(POST): {tool_names}")
                                        return tools

                                except json.JSONDecodeError as e:
                                    print(f"  ❌ POST JSON解析失败: {str(e)}")
                                except Exception as e:
                                    print(f"  ❌ POST处理失败: {str(e)}")
                            else:
                                print(f"  ⚠️ POST请求失败: {response.status_code} - {response.text[:100]}")

                        except requests.RequestException as e:
                            print(f"  ⚠️ POST请求异常: {str(e)}")
                            continue

                # 如果所有端点都失败，尝试一些特殊的端点
                print("⚠️ 常规端点失败，尝试ModelScope特定端点...")

                # ModelScope特定的端点
                special_endpoints = [
                    sse_url.replace('/sse', '/api/v1/tools'),
                    sse_url.replace('/sse', '/v1/tools'),
                    sse_url.replace('/sse', '/mcp/v1/tools'),
                    sse_url.replace('/sse', '/tools/list'),
                    sse_url.replace('/sse', '/api/tools/list'),
                    sse_url.replace('/sse', '/capabilities'),
                    sse_url.replace('/sse', '/info'),
                    sse_url.replace('/sse', '/schema'),
                ]

                for endpoint in special_endpoints:
                    try:
                        print(f"🔗 尝试特殊端点: {endpoint}")
                        response = requests.get(endpoint, timeout=10)
                        print(f"📡 特殊端点响应: {response.status_code}")

                        if response.status_code == 200:
                            try:
                                result = response.json()
                                print(f"📋 特殊端点响应内容: {result}")

                                tools_data = self._parse_tools_response(result)
                                if tools_data:
                                    tools = self._format_tools(tools_data)
                                    tool_names = [tool['function']['name'] for tool in tools]
                                    print(f"✅ 特殊端点工具加载成功: {tool_names}")
                                    return tools
                            except Exception as e:
                                print(f"❌ 特殊端点处理失败: {str(e)}")
                    except Exception as e:
                        print(f"❌ 特殊端点请求失败: {str(e)}")

                # 最后：基于ModelScope MCP推测工具
                print("⚠️ 所有端点都失败，基于ModelScope MCP推测可能的工具")
                inferred_tools = [
                    {
                        "type": "function",
                        "function": {
                            "name": "fetch_url",
                            "description": "Fetch content from a URL using ModelScope MCP",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "url": {
                                        "type": "string",
                                        "description": "The URL to fetch"
                                    }
                                },
                                "required": ["url"]
                            }
                        }
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "search_web",
                            "description": "Search the web using ModelScope MCP",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "description": "Search query"
                                    }
                                },
                                "required": ["query"]
                            }
                        }
                    },
                    {
                        "type": "function",
                        "function": {
                            "name": "get_weather",
                            "description": "Get weather information using ModelScope MCP",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "location": {
                                        "type": "string",
                                        "description": "Location for weather query"
                                    }
                                },
                                "required": ["location"]
                            }
                        }
                    }
                ]
                tool_names = [tool['function']['name'] for tool in inferred_tools]
                print(f"📦 推测工具已加载: {tool_names}")
                print("💡 提示：这些是基于ModelScope MCP推测的工具，实际可用性需要测试")
                return inferred_tools

            except Exception as e:
                print(f"❌ 获取SSE工具时发生错误: {str(e)}")
                return []
        else:
            print("❌ 未知的服务器类型")
            return []

    async def call_tool(self, tool_name: str, arguments: dict):
        """Call a specific MCP tool"""
        if not self.is_connected():
            raise RuntimeError("Not connected to MCP server")

        if self.server_type == 'fastmcp':
            if tool_name == "generate_image":
                prompt = arguments.get("prompt", "")
                return f"图像生成请求已处理: {prompt}"
            elif tool_name == "text_to_speech":
                text = arguments.get("text", "")
                return f"语音合成请求已处理: {text}"
            else:
                return f"未知工具: {tool_name}"
        elif self.server_type == 'stdio':
            try:
                if not self.session:
                    raise RuntimeError("Stdio会话未初始化")

                # 使用官方示例的方法调用工具
                result = await self.session.call_tool(tool_name, arguments)
                print(f"🔧 Stdio工具调用成功: {tool_name} -> {result.content}")
                return str(result.content)
            except Exception as e:
                print(f"❌ Stdio工具调用失败: {tool_name} - {str(e)}")
                raise RuntimeError(f"Stdio工具调用失败: {str(e)}")
        elif self.server_type == 'sse':
            # 对于SSE服务器，发送工具调用请求
            try:
                import requests

                print(f"🔧 调用SSE工具: {tool_name} 参数: {arguments}")

                # 构建工具调用请求
                request_data = {
                    "jsonrpc": "2.0",
                    "id": 2,
                    "method": "tools/call",
                    "params": {
                        "name": tool_name,
                        "arguments": arguments
                    }
                }

                # 发送POST请求到SSE服务器
                sse_url = getattr(self, 'sse_url', None)
                if sse_url:
                    # 尝试多个可能的端点
                    endpoints_to_try = [
                        sse_url.replace('/sse', '/call'),  # 替换sse为call
                        sse_url.replace('/sse', ''),  # 移除sse后缀
                        sse_url + '/call',  # 添加call路径
                        sse_url.replace('/sse', '/api/call'),  # API前缀
                    ]

                    for endpoint in endpoints_to_try:
                        try:
                            print(f"🔗 尝试调用端点: {endpoint}")
                            response = requests.post(
                                endpoint,
                                json=request_data,
                                headers={'Content-Type': 'application/json'},
                                timeout=30
                            )

                            print(f"📡 工具调用响应状态码: {response.status_code}")

                            if response.status_code == 200:
                                result = response.json()
                                print(f"📋 工具调用响应: {result}")

                                if 'result' in result:
                                    return str(result['result'])
                                elif 'error' in result:
                                    raise RuntimeError(f"SSE工具调用错误: {result['error']}")
                                else:
                                    return str(result)
                            else:
                                print(f"❌ 端点 {endpoint} 返回状态码: {response.status_code}")
                                continue

                        except requests.RequestException as e:
                            print(f"❌ 请求端点 {endpoint} 失败: {str(e)}")
                            continue

                    # 如果所有端点都失败
                    raise RuntimeError(f"所有SSE端点都无法访问，工具调用失败")
                else:
                    raise RuntimeError("SSE服务器URL未设置")

            except Exception as e:
                print(f"❌ SSE工具调用异常: {str(e)}")
                raise RuntimeError(f"SSE工具调用失败: {str(e)}")
        else:
            return f"工具调用已发送: {tool_name}"



# MCP Server Manager class
class MCPServerManager:
    def __init__(self):
        self.servers = {}  # 存储运行中的MCP服务器实例 {server_name: {'instance': mcp_instance, 'task': task}}
        self.loaded_configs = []  # 存储已加载的配置
        
    def load_from_json(self, json_config):
        """Load MCP server configuration from JSON string"""
        try:
            config = json.loads(json_config)
            config_name = config.get("server_name", "unnamed")
            
            # 检查同名配置是否已存在
            for i, cfg in enumerate(self.loaded_configs):
                if cfg.get("server_name") == config_name:
                    # 更新已有配置
                    self.loaded_configs[i] = config
                    # 如果服务器正在运行，重启它
                    if config_name in self.servers and not self.servers[config_name]['task'].done():
                        self.servers[config_name]['task'].cancel()
                        self.servers.pop(config_name)
                    return True, f"Configuration '{config_name}' updated successfully"
            
            # 添加新配置
            self.loaded_configs.append(config)
            return True, f"Configuration '{config_name}' loaded successfully"
        except json.JSONDecodeError as e:
            return False, f"Invalid JSON: {str(e)}"
        except Exception as e:
            return False, f"Error loading configuration: {str(e)}"
    
    async def start_server(self, server_name=None):
        """Start MCP server with specified configuration"""
        # 如果没有指定服务器名称，使用第一个配置
        if not server_name:
            if not self.loaded_configs:
                return False, "No configurations loaded"
            server_name = self.loaded_configs[0].get("server_name", "unnamed")
            
        # 检查服务器是否已在运行
        if server_name in self.servers and not self.servers[server_name]['task'].done():
            return False, f"Server '{server_name}' is already running"
            
        # 查找对应的配置
        config = next((cfg for cfg in self.loaded_configs if cfg.get("server_name") == server_name), None)
        if not config:
            return False, f"Configuration for '{server_name}' not found"
            
        try:
            # Create new MCP instance with configuration
            mcp = FastMCP()
            
            # Add tools based on configuration
            tools_config = config.get("tools", [])
            for tool in tools_config:
                tool_name = tool.get("name")
                tool_desc = tool.get("description")
                tool_impl = tool.get("implementation", {})
                if tool_name and tool_desc:
                    # Register the tool with the MCP server
                    # This is a placeholder - actual implementation depends on FastMCP API
                    # 提取函数实现和模式
                    tool_func = lambda **kwargs: tool_impl
                    tool_schema = {}
                    mcp.register_tool(
                        name=tool_name,
                        func=tool_func,
                        schema=tool_schema
                    )
                    print(f"Added tool: {tool_name} - {tool_desc}")
            
            # 直接启动FastMCP服务器
            mcp.running = True
            print(f"FastMCP server '{server_name}' started successfully")

            # 创建一个简单的保持任务
            async def keep_alive():
                while mcp.running:
                    await asyncio.sleep(1)

            task = asyncio.create_task(keep_alive())
            self.servers[server_name] = {'instance': mcp, 'task': task}
            return True, f"MCP server '{server_name}' started successfully"
        except Exception as e:
            return False, f"Error starting server: {str(e)}"
    

    
    def stop_server(self, server_name=None):
        """Stop MCP server"""
        if not server_name:
            # 停止所有服务器
            for name in list(self.servers.keys()):
                server = self.servers[name]
                # 停止FastMCP实例
                if 'instance' in server:
                    server['instance'].running = False
                # 取消任务
                if not server['task'].done():
                    server['task'].cancel()
                self.servers.pop(name)
            return True, "All servers stopped"

        if server_name in self.servers:
            server = self.servers[server_name]
            # 停止FastMCP实例
            if 'instance' in server:
                server['instance'].running = False
            # 取消任务
            if not server['task'].done():
                server['task'].cancel()
            self.servers.pop(server_name)
            return True, f"Server '{server_name}' stopped"
        return False, f"Server '{server_name}' not found"
    
    async def call_tool(self, server_name, tool_name, **params):
        """Call a tool on the specified MCP server"""
        if server_name not in self.servers or self.servers[server_name]['task'].done():
            return False, f"Server '{server_name}' is not running"
            
        mcp_instance = self.servers[server_name]['instance']
        try:
            # 调用MCP服务器上的工具
            result = await mcp_instance.call_tool(tool_name, **params)
            return True, result
        except Exception as e:
            return False, f"Error calling tool '{tool_name}': {str(e)}"
    
    def get_running_servers(self):
        """Get list of running server names"""
        return [name for name, server in self.servers.items() if not server['task'].done()]
    
    def get_loaded_configs(self):
        """Get list of loaded configurations"""
        return self.loaded_configs
    
    def clear_all_configs(self):
        """Stop all servers and clear all configurations"""
        # Stop all running servers
        self.stop_server()
        # Clear loaded configurations
        self.loaded_configs = []
        return True, "All MCP configurations cleared successfully"

# 完全独立的多模型聊天应用
class MultiModelChatApp:
    def __init__(self):
        self.chat_history = []
        self.api_keys = {} 
        self.full_history = []  # 新增完整历史记录存储
        self.mcp_manager = MCPServerManager()  # 创建MCP服务器管理器实例
        self.mcp_client = None  # MCP客户端实例
        self.mcp_config_updated_callback = None  # MCP配置更新回调函数
        
        # 模型配置
        self.models = {
            "openai": {
                "name": "OpenAI",
                "base_url": os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1"),
                "submodels": ["gpt-4o", "gpt-4", "gpt-3.5-turbo"],
                "default": "gpt-4o"
            },
            "zhipu": {
                "name": "智谱AI",
                "base_url": "https://open.bigmodel.cn/api/paas/v4/chat/completions",
                "submodels": ['GLM-4.5-Flash',"glm-4", "glm-4v", "glm-3-turbo", "glm-4-air"],
                "default": "GLM-4.5-Flash"
            },
            "deepseek": {
                "name": "DeepSeek", 
                "base_url": "https://api.deepseek.com/v1/chat/completions",
                "submodels": ["deepseek-chat", "deepseek-vl"],
                "default": "deepseek-chat"
            },
            "qwen": {
            "name": "通义千问",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "submodels": ["qwen-plus", "qwen-max", "qwen-vl-plus", "deepseek-r1"],
            "default": "qwen-plus"
        }
        }
    
    # 处理MCP JSON配置的方法
    def handle_mcp_json_config(self, json_config):
        if not json_config.strip():
            return "错误: 配置不能为空"

        try:
            # 解析JSON配置
            config = json.loads(json_config)
            
            # 检查是否是用户提供的格式 (包含mcpServers字段)
            if "mcpServers" in config:
                # 提取第一个服务器配置
                server_name = next(iter(config["mcpServers"]))
                server_config = config["mcpServers"][server_name]

                # 添加服务器名称到配置
                server_config["server_name"] = server_name

                # 智能配置处理：如果是stdio配置但命令不存在，添加SSE回退
                if 'command' in server_config and server_name == 'fetch':
                    # 为fetch服务器添加SSE回退URL
                    if 'url' not in server_config:
                        server_config['url'] = "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"
                        print(f"💡 为 {server_name} 添加SSE回退URL")

                # 加载配置
                self.mcp_manager.loaded_configs = [server_config]
            else:
                # 直接使用配置
                server_name = config.get("server_name", "unnamed")
                server_config = config

                # 加载配置
                success, message = self.mcp_manager.load_from_json(json_config)
                if not success:
                    return f"配置加载失败: {message}"
            
            # 启动服务器 (使用异步运行时)
            success, message = asyncio.run(self.mcp_manager.start_server(server_name))
            if success:
                # 更新MCP客户端
                mcp_config = {
                    "mcpServers": {
                        server_name: server_config
                    }
                }
                self.mcp_client = MCPClient(mcp_config)
                asyncio.run(self.mcp_client.connect_to_server(server_name))

                # 获取工具列表
                try:
                    tools = asyncio.run(self.mcp_client.get_available_tools())
                    if tools:
                        tool_names = [tool['function']['name'] for tool in tools]
                        tools_info = f"\n✅ 成功加载 {len(tools)} 个工具: {', '.join(tool_names)}"
                    else:
                        tools_info = "\n⚠️ 连接成功但未获取到工具"
                except Exception as e:
                    tools_info = f"\n❌ 获取工具列表失败: {str(e)}"

                # 通知配置已更新
                if self.mcp_config_updated_callback:
                    self.mcp_config_updated_callback(server_name)
                return f"✅ 成功: {message}\nMCP客户端已成功初始化并连接到服务器{tools_info}"
            else:
                return f"❌ 服务器启动失败: {message}"
        except json.JSONDecodeError as e:
            return f"无效的JSON: {str(e)}"
        except Exception as e:
            return f"处理MCP配置时出错: {str(e)}"

    # 设置MCP配置更新回调函数
    def set_mcp_config_updated_callback(self, callback):
        self.mcp_config_updated_callback = callback
        
    def update_mcp_display(self):
        configs = self.mcp_manager.get_loaded_configs()
        servers = self.mcp_manager.get_running_servers()
        status = "无运行中的MCP服务"
        if servers:
            status = "运行中的MCP服务: " + ", ".join(servers.keys())
        return configs, status

    # 获取已加载MCP配置的方法
    def get_mcp_configs(self):
        return self.mcp_manager.get_loaded_configs()
    
    # 清空MCP配置的方法
    def clear_mcp_configs(self):
        success, message = self.mcp_manager.clear_all_configs()
        # 安全关闭MCP客户端连接
        if self.mcp_client:
            try:
                # 使用新的事件循环来避免冲突
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    loop.run_until_complete(self.mcp_client.cleanup())
                finally:
                    loop.close()
                print("✅ MCP客户端已安全关闭")
            except Exception as e:
                print(f"⚠️ MCP客户端关闭警告: {str(e)}")
            finally:
                self.mcp_client = None
        return message
     
    # 异步版本的处理MCP配置方法，供内部使用
    async def _async_handle_mcp_json_config(self, json_config):
        """Handle MCP server configuration from JSON input"""
        if not json_config.strip():
            return "错误: 配置不能为空"

        try:
            # 解析JSON配置
            config = json.loads(json_config)
            
            # 检查是否是用户提供的格式 (包含mcpServers字段)
            if "mcpServers" in config:
                # 提取第一个服务器配置
                server_name = next(iter(config["mcpServers"]))
                server_config = config["mcpServers"][server_name]
                
                # 添加服务器名称到配置
                server_config["server_name"] = server_name
                
                # 加载配置
                self.mcp_manager.loaded_configs = [server_config]
                
                # 启动服务器
                success, message = asyncio.run(self.mcp_manager.start_server(server_name))
                if success:
                    # 更新MCP客户端
                    mcp_config = {
                        "mcpServers": {
                            server_name: server_config
                        }
                    }
                    self.mcp_client = MCPClient(mcp_config)
                    asyncio.run(self.mcp_client.connect_to_server(server_name))
                    return f"成功: {message}"
                else:
                    return f"服务器启动失败: {message}"
            else:
                # 使用原有逻辑处理我们期望的格式
                success, message = self.mcp_manager.load_from_json(json_config)
                if not success:
                    return f"配置加载失败: {message}"

                # 尝试启动第一个服务器
                server_name = self.mcp_manager.loaded_configs[0].get("server_name", "unnamed")
                success, message = asyncio.run(self.mcp_manager.start_server(server_name))
                if success:
                    # 更新MCP客户端
                    mcp_config = {
                        "mcpServers": {
                            server_name: self.mcp_manager.loaded_configs[0]
                        }
                    }
                    self.mcp_client = MCPClient(mcp_config)
                    asyncio.run(self.mcp_client.connect_to_server(server_name))
                    return f"成功: {message}"
                else:
                    return f"服务器启动失败: {message}"
        except json.JSONDecodeError as e:
            return f"无效的JSON: {str(e)}"
        except Exception as e:
            return f"处理MCP配置时出错: {str(e)}"

    def create_interface(self):
        with gr.Blocks(title="多模型AI助手") as app:
            gr.Markdown("# 🤖 多模型AI助手")
            gr.Markdown("支持智谱AI、DeepSeek、通义千问和MCP服务")
            
            # 添加标签页
            with gr.Tabs():
                # 聊天标签页
                with gr.TabItem("聊天"):
                    with gr.Row():
                        # 左侧控制面板
                        with gr.Column(scale=1):
                            gr.Markdown("### ⚙️ 模型配置")
                            
                            # 模型选择
                            model_choice = gr.Radio(
                                choices=["zhipu", "deepseek", "qwen"],
                                value="zhipu",
                                label="选择AI模型"
                            )
                            
                            # 子模型选择
                            submodel_choice = gr.Dropdown(
                                choices=self.models["zhipu"]["submodels"],
                                value=self.models["zhipu"]["default"],
                                label="模型变体",
                                # 添加allow_custom_value=True以支持自定义值
                                allow_custom_value=True
                            )
                            
                            # API密钥
                            api_key_input = gr.Textbox(
                                label="API密钥",
                                type="password",
                                value="a95934598bc145328b2a1170e251242e.w5JWPbRPrER3cLYW"
                            )
                            
                            # 自定义提示词输入框
                            custom_prompt = gr.Textbox(
                                label="自定义提示词",
                                placeholder="请输入系统提示词...",
                                lines=3
                            )
                            
                            # 新增base_url输入框
                            base_url_input = gr.Textbox(
                                label="API基础URL",
                                value=self.models["zhipu"]["base_url"],
                                placeholder="输入模型的基础URL"
                            )
                            
                            # 新增添加模型表单
                            with gr.Accordion("添加新模型", open=False):
                                new_model_name = gr.Textbox(label="模型代号")
                                new_model_display_name = gr.Textbox(label="模型显示名称")
                                new_model_base_url = gr.Textbox(label="基础URL")
                                new_model_submodels = gr.Textbox(label="子模型列表（逗号分隔）")
                                new_model_default = gr.Textbox(label="默认子模型")
                                add_model_btn = gr.Button("添加模型")
                            
                            # 新增更新子模型表单
                            with gr.Accordion("更新子模型", open=False):
                                model_to_update = gr.Dropdown(
                                    choices=list(self.models.keys()),
                                    label="选择要更新的模型"
                                )
                                new_submodels = gr.Textbox(label="新子模型列表（逗号分隔）")
                                new_default_submodel = gr.Textbox(label="新默认子模型")
                                update_submodels_btn = gr.Button("更新子模型")

                            # 新增增加子模型表单
                            with gr.Accordion("增加子模型", open=False):
                                model_to_add_sub = gr.Dropdown(
                                    choices=list(self.models.keys()),
                                    label="选择要添加子模型的模型"
                                )
                                new_submodel = gr.Textbox(label="新增子模型名称")
                                set_as_default = gr.Checkbox(label="设为默认子模型", value=False)
                                add_submodel_btn = gr.Button("增加子模型")
                            
                            # 温度参数
                            temperature = gr.Slider(
                                0.0, 1.0, 0.7, 0.1,
                                label="温度"
                            )

                            # MCP服务设置
                            use_mcp = gr.Checkbox(label="使用MCP服务", value=False)

                            # 显示当前MCP配置和状态
                            mcp_config_display = gr.JSON(
                                label="当前MCP配置"
                            )

                            # 显示已加载的工具列表
                            mcp_tools_display = gr.Textbox(
                                label="已加载的MCP工具",
                                interactive=False,
                                lines=3,
                                placeholder="暂无工具加载"
                            )

                            # 本地状态显示组件
                            mcp_service_status = gr.Textbox(
                                label="MCP服务操作状态",
                                interactive=False
                            )

                            # 更新工具显示的函数
                            def update_tools_display():
                                if self.mcp_client and self.mcp_client.is_connected():
                                    try:
                                        tools = asyncio.run(self.mcp_client.get_available_tools())
                                        if tools:
                                            tool_names = [tool['function']['name'] for tool in tools]
                                            # 如果工具太多，只显示前10个，然后显示总数
                                            if len(tool_names) > 10:
                                                displayed_tools = tool_names[:10]
                                                return f"✅ 已加载 {len(tools)} 个工具: {', '.join(displayed_tools)}... (共{len(tools)}个)"
                                            else:
                                                return f"✅ 已加载 {len(tools)} 个工具: {', '.join(tool_names)}"
                                        else:
                                            return "⚠️ 已连接但未获取到工具"
                                    except Exception as e:
                                        return f"❌ 获取工具列表失败: {str(e)}"
                                else:
                                    return "❌ MCP服务未连接"

                            # MCP服务复选框事件处理
                            def toggle_mcp_service(use_mcp_value):
                                if use_mcp_value:
                                    # 检查是否已有配置的MCP服务
                                    existing_configs = self.mcp_manager.get_loaded_configs()

                                    if existing_configs:
                                        # 如果有已配置的服务，启动所有已配置的服务
                                        results = []
                                        last_server_name = None

                                        for config in existing_configs:
                                            server_name = config.get("server_name", "unknown")
                                            success, message = asyncio.run(self.mcp_manager.start_server(server_name))
                                            results.append(f"{server_name}: {message}")
                                            if success:
                                                last_server_name = server_name

                                        # 连接客户端到最后一个成功启动的服务器
                                        if last_server_name:
                                            try:
                                                # 获取服务器配置
                                                server_config = next((cfg for cfg in existing_configs if cfg.get("server_name") == last_server_name), None)
                                                if server_config:
                                                    mcp_config = {
                                                        "mcpServers": {
                                                            last_server_name: server_config
                                                        }
                                                    }
                                                    self.mcp_client = MCPClient(mcp_config)
                                                    asyncio.run(self.mcp_client.connect_to_server(last_server_name))
                                                    results.append(f"✅ 客户端已连接到 {last_server_name}")

                                                    # 获取并显示工具列表
                                                    tools_info = update_tools_display()
                                                    results.append(tools_info)
                                            except Exception as e:
                                                results.append(f"❌ 客户端连接失败: {str(e)}")

                                        return "启动已配置的MCP服务: " + "; ".join(results)
                                    else:
                                        # 如果没有已配置的服务，使用默认配置
                                        default_config = {
                                            "server_name": "default-mcp",
                                            "tools": [
                                                {
                                                    "name": "generate_image",
                                                    "description": "Generate an image"
                                                },
                                                {
                                                    "name": "run_dia_tts",
                                                    "description": "Text-to-Speech Synthesis"
                                                }
                                            ]
                                        }
                                        json_config = json.dumps(default_config)
                                        result = self.handle_mcp_json_config(json_config)
                                        # 添加工具信息
                                        tools_info = update_tools_display()
                                        return f"{result}\n{tools_info}"
                                else:
                                    # 停止所有MCP服务
                                    if self.mcp_client:
                                        asyncio.run(self.mcp_client.cleanup())
                                        self.mcp_client = None
                                    return self.mcp_manager.stop_server()[1]

                            # 更新MCP配置显示
                            def update_mcp_display():
                                configs = self.mcp_manager.get_loaded_configs()
                                return configs

                            use_mcp.change(
                                fn=toggle_mcp_service,
                                inputs=[use_mcp],
                                outputs=[mcp_service_status]
                            ).then(
                                fn=update_mcp_display,
                                inputs=[],
                                outputs=[mcp_config_display]
                            ).then(
                                fn=update_tools_display,
                                inputs=[],
                                outputs=[mcp_tools_display]
                            )

                            # 初始化MCP配置显示
                            configs = update_mcp_display()
                            mcp_config_display.value = configs

                            # 初始化显示已在上方完成
                            # mcp_config_display.value = update_mcp_config_and_status()

                            # 配置更新回调函数
                            def on_mcp_config_updated(server_name):
                                configs = update_mcp_display()
                                mcp_config_display.value = configs
                                return gr.Info(f"MCP服务 '{server_name}' 已更新")

                            # 设置配置更新回调
                            self.set_mcp_config_updated_callback(on_mcp_config_updated)

                            # 添加一个定时器定期更新MCP配置和工具显示
                            # 根据Timer组件文档，使用正确的参数
                            timer = gr.Timer(value=2, active=True)
                            timer.tick(
                                fn=update_mcp_display,
                                inputs=[],
                                outputs=[mcp_config_display]
                            ).then(
                                fn=update_tools_display,
                                inputs=[],
                                outputs=[mcp_tools_display]
                            )

                            # 添加一个隐藏按钮用于触发配置更新
                            update_mcp_btn = gr.Button(visible=False, elem_id='update_mcp')
                            update_mcp_btn.click(
                                update_mcp_display,
                                inputs=[],
                                outputs=[mcp_config_display]
                            )

                            # 每隔1秒更新一次配置和状态
                            gr.HTML("""
                            <script>
                            setInterval(() => {
                                gradioApp().getElementById('update_mcp').click();
                            }, 1000);
                            </script>
                            """)

                            # 测试API按钮
                            test_btn = gr.Button("测试API连接")
                            test_result = gr.Textbox(label="测试结果")
                           # 自定义提示词设置
                            with gr.Accordion("自定义提示词设置", open=False):
                                advanced_custom_prompt = gr.TextArea(
                                    label="高级提示词设置",
                                    lines=5,
                                    placeholder="请输入自定义提示词..."
                                )
                        
                        # 右侧对话区域
                        with gr.Column(scale=3):
                            # # 新增回退按钮行
                            # with gr.Row():
                            #     undo_btn = gr.Button("↩️ 回退", variant="secondary")
                            # 聊天历史
                            chatbot = gr.Chatbot(
                                height=500,
                                type="messages",
                            )
                            
                            # 回退按钮
                            undo_btn = gr.Button("↩️ 回退", variant="secondary")
                            
                            # 修改后的清除对话函数
                            def clear_chat():
                                self.full_history.extend(self.chat_history)  # 保存到完整历史
                                self.chat_history = []  # 清空当前聊天记录
                                return []  # 返回空列表更新界面
                            
                            # 新增查看历史记录按钮
                            view_history_btn = gr.Button("查看历史记录")
                            # 查看历史记录函数
                            def view_history():
                                 return self.full_history + self.chat_history
                            view_history_btn.click(view_history, None, chatbot)
                            # 修改后的回退功能
                            def undo_last_message(current_history):
                                if len(current_history) >= 2:
                                    new_history = current_history[:-2]
                                    self.chat_history = new_history  # ✅ 同步更新内存状态
                                    return new_history
                                else:
                                    self.chat_history = current_history  # ✅ 保持内存状态一致
                                    return current_history
                            
                            undo_btn.click(
                                undo_last_message,
                                inputs=[chatbot],
                                outputs=[chatbot]
                            )
                            with gr.Row(): 
                                # 输入框
                                msg = gr.Textbox(
                                    label="输入消息",
                                    placeholder="请输入您的问题...",
                                    scale=4
                                )
                                with gr.Column():
                                    send_btn = gr.Button("发送", variant="primary", scale=1)
                                    # 使用 Gradio 的 ClearButton 组件（仅保留此处）
                                    clear_btn = gr.ClearButton([chatbot])
                                    clear_btn.click(clear_chat, None, chatbot, queue=False)
                            
                            # 文件上传
                            file_upload = gr.File(
                                label="上传文件",
                                file_types=["image", "text"]
                            )

                # 配置标签页（高级选项）
                with gr.TabItem("配置"):
                    gr.Markdown("# MCP Server Configuration")
                    gr.Markdown("Enter JSON configuration to load MCP server")
                    
                    with gr.Row():
                        with gr.Column(scale=3):
                            json_input = gr.Textbox(
                                label="JSON Configuration",
                                lines=10,
                                placeholder="Enter your MCP server JSON configuration here..."
                            )
                            
                            with gr.Row():
                                load_btn = gr.Button("Load MCP Server")
                                stop_btn = gr.Button("Stop MCP Server")
                                clear_btn = gr.Button("Clear MCP List")
                            
                        with gr.Column(scale=1):
                            status_output = gr.Textbox(
                                label="Status",
                                lines=5,
                                interactive=False
                            )
                    
                    # 已加载的MCP配置列表
                    gr.Markdown("## Loaded MCP Configurations")
                    configs_output = gr.JSON(
                        label="Loaded Configs"
                    )
                    
                    # Example JSON configuration
                    example_json = json.dumps({
                        "server_name": "gradio-spaces",
                        "tools": [
                            {
                                "name": "generate_image",
                                "description": "Generate an image using Flux"
                            },
                            {
                                "name": "run_dia_tts",
                                "description": "Text-to-Speech Synthesis"
                            }
                        ]
                    }, indent=2)
                    
                    gr.Examples(
                        examples=[[example_json]],
                        inputs=[json_input]
                    )

            # 模型切换时更新子模型

            # 模型切换时更新子模型和base_url
            def update_submodel_and_url(model_key):
                model_info = self.models[model_key]
                return (
                    gr.Dropdown(choices=model_info["submodels"], value=model_info["default"]),
                    gr.Textbox(value=model_info["base_url"])
                )
            
            model_choice.change(
                update_submodel_and_url,
                inputs=[model_choice],
                outputs=[submodel_choice, base_url_input]
            )

            # MCP配置相关事件绑定
            load_btn.click(
                fn=self.handle_mcp_json_config,
                inputs=[json_input],
                outputs=[status_output]
            ).then(
                fn=update_mcp_display,
                inputs=[],
                outputs=[mcp_config_display]
            ).then(
                fn=update_tools_display,
                inputs=[],
                outputs=[mcp_tools_display]
            )

            stop_btn.click(
                fn=lambda: self.mcp_manager.stop_server()[1],
                inputs=[],
                outputs=[status_output]
            ).then(
                fn=update_mcp_display,
                inputs=[],
                outputs=[mcp_config_display]
            )

            # 绑定清空MCP列表按钮事件
            clear_btn.click(
                fn=self.clear_mcp_configs,
                inputs=[],
                outputs=[status_output]
            ).then(
                fn=update_mcp_display,
                inputs=[],
                outputs=[mcp_config_display]
            )

            # 定义配置更新函数
            def update_configs(): 
                return self.mcp_manager.get_loaded_configs()

            # 应用加载时更新一次配置
            app.load(update_configs, None, configs_output)

            # 当状态更新时，也更新配置列表
            status_output.change(
                fn=update_configs,
                inputs=[],
                outputs=[configs_output]
            )

            # 添加一个隐藏按钮用于触发更新
            update_btn = gr.Button(visible=False, elem_id='loaded_configs')
            update_btn.click(
                fn=update_configs,
                inputs=[],
                outputs=[configs_output]
            )
            
            # 每隔1秒更新一次配置（使用HTML嵌入JavaScript）
            gr.HTML("""
            <script>
            setInterval(() => {
                gradioApp().getElementById('loaded_configs').click();
            }, 1000);
            </script>
            """)
            
            # 保存修改后的base_url
            def save_base_url(model_key, new_base_url):
                self.models[model_key]["base_url"] = new_base_url
                return gr.Info(f"{self.models[model_key]['name']}的基础URL已更新！")
            
            base_url_input.change(
                save_base_url,
                inputs=[model_choice, base_url_input],
                outputs=[]
            )
            # 添加新模型
            def add_model(name, display_name, base_url, submodels_str, default_submodel):
                if not all([name, display_name, base_url, submodels_str, default_submodel]):
                    return gr.Error("请填写所有字段！")
                
                submodels = [s.strip() for s in submodels_str.split(",") if s.strip()]
                if default_submodel not in submodels:
                    return gr.Error("默认子模型不在子模型列表中！")
                
                self.models[name] = {
                    "name": display_name,
                    "base_url": base_url,
                    "submodels": submodels,
                    "default": default_submodel
                }
                
                # 更新模型选择和子模型更新下拉框
                return (
                    gr.Radio.update(choices=list(self.models.keys())),
                    gr.Dropdown.update(choices=list(self.models.keys()))
                )
            
            add_model_btn.click(
                add_model,
                inputs=[new_model_name, new_model_display_name, new_model_base_url, new_model_submodels, new_model_default],
                outputs=[model_choice, model_to_update]
            )

            # 更新子模型
            def update_submodels(model_key, submodels_str, default_submodel):
                if not model_key or not submodels_str or not default_submodel:
                    return gr.Error("请填写所有字段！")
                
                submodels = [s.strip() for s in submodels_str.split(",") if s.strip()]
                if default_submodel not in submodels:
                    return gr.Error("默认子模型不在子模型列表中！")
                
                if model_key in self.models:
                    self.models[model_key]["submodels"] = submodels
                    self.models[model_key]["default"] = default_submodel
                    return gr.Info(f"{self.models[model_key]['name']}的子模型已更新！")
                else:
                    return gr.Error("未找到该模型！")
            
            update_submodels_btn.click(
                update_submodels,
                inputs=[model_to_update, new_submodels, new_default_submodel],
                outputs=[]
            )
            # 增加子模型
            def add_submodel(model_key, new_sub, set_default):
                if not model_key or not new_sub:
                    return gr.Error("请填写模型和子模型名称！")
                
                if model_key in self.models:
                    if new_sub in self.models[model_key]["submodels"]:
                        return gr.Error("该子模型已存在！")
                    
                    self.models[model_key]["submodels"].append(new_sub)
                    if set_default:
                        self.models[model_key]["default"] = new_sub
                    
                    # 使用gr.update显式更新下拉框选项
                    return gr.update(choices=self.models[model_key]["submodels"], value=new_sub if set_default else None)
                else:
                    return gr.Error("未找到该模型！")
            # 更新子模型选择下拉框的函数
            def update_submodel_choices(model_key):
                if model_key in self.models:
                    return self.models[model_key]["submodels"]
                return []

            add_submodel_btn.click(
                add_submodel,
                inputs=[model_to_add_sub, new_submodel, set_as_default],
                outputs=[submodel_choice]
            ).then(
                # 强制刷新子模型选项
                lambda model_key: gr.update(choices=self.models[model_key]["submodels"]),
                inputs=[model_to_add_sub],
                outputs=[submodel_choice]
            )
            # # 监听模型选择变化，更新子模型下拉框
            # model_choice.change(
            #     update_submodel_choices,
            #     inputs=[model_choice],
            #     outputs=[submodel_choice]
            # )
            # 测试API连接
            def test_api(model_key, submodel, api_key):
                try:
                    model_info = self.models[model_key]
                    
                    if model_key == "zhipu":
                        headers = {
                            "Authorization": f"Bearer {api_key}",
                            "Content-Type": "application/json"
                        }
                        payload = {
                            "model": submodel,
                            "messages": [{"role": "user", "content": "测试消息"}],
                            "temperature": 0.7,
                            "max_tokens": 100
                        }
                        
                        response = requests.post(
                            model_info["base_url"], 
                            json=payload, 
                            headers=headers, 
                            timeout=30
                        )
                        
                        if response.status_code == 200:
                            return "✅ 智谱AI API连接成功！"
                        else:
                            return f"❌ 智谱AI API调用失败 (状态码: {response.status_code})"
                    
                    elif model_key == "deepseek":
                        headers = {"Authorization": f"Bearer {api_key}"}
                        payload = {
                            "model": submodel,
                            "messages": [{"role": "user", "content": "测试消息"}],
                            "temperature": 0.7,
                            "max_tokens": 100
                        }
                        response = requests.post(model_info["base_url"], json=payload, headers=headers, timeout=30)
                        
                        if response.status_code == 200:
                            return "✅ DeepSeek API连接成功！"
                        else:
                            return f"❌ DeepSeek API调用失败 (状态码: {response.status_code})"
                    
                    elif model_key == "qwen":
                        try:
                            # 使用OpenAI兼容客户端测试通义千问模型
                            client = OpenAI(
                                api_key=api_key,
                                base_url=model_info["base_url"]
                            )
                            
                            # 构建测试消息
                            messages = [
                                {"role": "user", "content": "测试消息"}
                            ]
                            
                            # 调用API
                            completion = client.chat.completions.create(
                                model=submodel,
                                messages=messages,
                                temperature=0.7,
                                max_tokens=100
                            )
                            
                            return "✅ 通义千问API连接成功！"
                        except Exception as e:
                            return f"❌ 通义千问API调用失败: {str(e)}"
                    
                except Exception as e:
                    return f"❌ 测试失败: {str(e)}"
            
            test_btn.click(
                test_api,
                inputs=[model_choice, submodel_choice, api_key_input],
                outputs=[test_result]
            )
            
            # 处理文件上传
            def handle_file(file):
                if not file:
                    return None
                    
                try:
                    file_path = file.name
                    file_name = os.path.basename(file_path)
                    
                    # 处理图片
                    if file_path.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                        img = PILImage.open(file_path)
                        img.thumbnail((400, 400))
                        # 返回图片消息
                        return {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": f"请分析这张图片: {file_name}"},
                                {"type": "image_url", "image_url": {"url": f"file://{file_path}"}}
                            ]
                        }
                    else:
                        # 处理文本文件
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()[:1000]  # 限制长度
                            return {
                                "role": "user",
                                "content": f"文件内容:\n{content}"
                            }
                        except:
                            return {
                                "role": "user",
                                "content": f"上传了文件: {file_name}"
                            }
                    
                except Exception as e:
                    return {
                        "role": "user",
                        "content": f"文件处理失败: {str(e)}"
                    }
            
            file_upload.change(
                handle_file,
                inputs=[file_upload],
                outputs=[msg]
            )
            
            # 处理MCP工具调用请求
            async def process_mcp_request(request):
                try:
                    # 解析请求
                    req_data = json.loads(request)
                    server_name = req_data.get("server_name")
                    tool_name = req_data.get("tool_name")
                    tool_params = req_data.get("params", {})

                    if not server_name or not tool_name:
                        return f"错误: 缺少服务器名称或工具名称"

                    # 调用MCP工具
                    success, result = await self.mcp_manager.call_tool(server_name, tool_name, **tool_params)
                    if success:
                        return f"MCP调用成功: {result}"
                    else:
                        return f"MCP调用失败: {result}"
                except Exception as e:
                    return f"处理MCP请求错误: {str(e)}"

            # 发送消息
            def chat(message, model_key, submodel, api_key, temp, custom_prompt, use_mcp, chatbot_value):
                if not message.strip(): 
                    return "", self.chat_history

                try:
                    # 保存API密钥
                    if api_key.strip(): 
                        self.api_keys[model_key] = api_key.strip()

                    current_api_key = self.api_keys.get(model_key) or api_key.strip()
                    if not current_api_key:
                        self.chat_history.append({"role": "assistant", "content": f"请输入{self.models[model_key]['name']}的API密钥"})
                        return "", self.chat_history

                    # 添加用户消息
                    self.chat_history.append({"role": "user", "content": message})
                    
                    # 显示思考状态
                    self.chat_history.append({"role": "assistant", "content": "AI正在思考..."})
                    yield "", self.chat_history
                    
                    # 移除思考状态
                    self.chat_history.pop()

                    # 准备MCP工具（所有模型通用，在所有分支之前定义）
                    tools = []
                    if use_mcp and self.mcp_client and self.mcp_client.is_connected():
                        try:
                            mcp_tools = asyncio.run(self.mcp_client.get_available_tools())
                            tools.extend(mcp_tools)
                            print(f"已加载 {len(mcp_tools)} 个MCP工具")
                        except Exception as e:
                            print(f"获取MCP工具失败: {str(e)}")

                    # 检查是否使用MCP服务
                    running_servers = self.mcp_manager.get_running_servers()
                    if use_mcp and running_servers and self.mcp_client and self.mcp_client.is_connected():
                        try:
                            # 使用MCP服务处理请求
                            result = asyncio.run(self.mcp_client.process_query(message))
                            self.chat_history.append({"role": "assistant", "content": result})
                        except Exception as e:
                            error_msg = f"MCP服务调用失败: {str(e)}\n请检查MCP服务状态或重新启动服务。"
                            self.chat_history.append({"role": "assistant", "content": error_msg})
                    elif message.startswith("mcp_call:"):
                        # 处理MCP调用
                        request = message[9:].strip()  # 去掉"mcp_call:"前缀
                        result = asyncio.run(process_mcp_request(request))
                        self.chat_history.append({"role": "assistant", "content": result})
                    elif model_key == "openai" and self.mcp_client:
                        # 使用OpenAI模型与MCP交互
                        result = asyncio.run(self.mcp_client.process_query(message))
                        self.chat_history.append({"role": "assistant", "content": result})
                    else:
                        # 调用API
                        model_info = self.models[model_key]

                        if model_key == "zhipu":
                            headers = {
                                "Authorization": f"Bearer {current_api_key}",
                                "Content-Type": "application/json"
                            }
                            # 构建包含系统提示词的消息列表
                            messages = []
                            # 使用默认系统提示词，除非用户提供了自定义提示词
                            default_system_prompt = "你好呀！我是你的智能助手，由深度求索（DeepSeek）研发，名字叫DeepSeek-R1。你可以把我当作一个随时在线、知识丰富又乐于助人的小伙伴～\n\n我能帮你做很多事情，比如：\n解答学习问题（语文、数学、英语、编程、历史……统统不在话下）\n写作助手（写作文、改简历、润色邮件、写小说、写剧本……我都能帮上忙）\n工作支持（做PPT、分析数据、写报告、制定计划……职场小能手就是我）\n生活百科（查天气、推荐美食、旅行攻略、健康小贴士……生活百事通）\n还有聊天解闷、讲笑话、陪你唠嗑\n\n无论你是学生、上班族、创作者，还是好奇宝宝，我都在这儿等你来撩～"
                            if custom_prompt.strip():
                                messages.append({"role": "system", "content": custom_prompt.strip()})
                            else:
                                messages.append({"role": "system", "content": default_system_prompt})
                            messages.append({"role": "user", "content": message})

                            payload = {
                                "model": submodel,
                                "messages": messages,
                                "temperature": temp,
                                "max_tokens": 2000
                            }

                            # 如果有MCP工具，添加到请求中
                            if tools:
                                payload["tools"] = tools
                                payload["tool_choice"] = "auto"
                            
                            response = requests.post(
                                model_info["base_url"], 
                                json=payload, 
                                headers=headers, 
                                timeout=120
                            )
                            
                            if response.status_code == 200:
                                result = response.json()
                                message = result.get("choices", [{}])[0].get("message", {})
                                content = message.get("content", "")
                                tool_calls = message.get("tool_calls", [])

                                # 处理工具调用
                                if tool_calls and self.mcp_client and self.mcp_client.is_connected():
                                    tool_results = []
                                    for tool_call in tool_calls:
                                        function = tool_call.get("function", {})
                                        tool_name = function.get("name", "")
                                        arguments = json.loads(function.get("arguments", "{}"))

                                        try:
                                            tool_result = asyncio.run(self.mcp_client.call_tool(tool_name, arguments))
                                            tool_results.append(f"🔧 调用工具 {tool_name}: {tool_result}")
                                        except Exception as e:
                                            tool_results.append(f"❌ 工具调用失败 {tool_name}: {str(e)}")

                                    # 组合内容和工具结果
                                    if content:
                                        final_content = content + "\n\n" + "\n".join(tool_results)
                                    else:
                                        final_content = "\n".join(tool_results)

                                    self.chat_history.append({"role": "assistant", "content": final_content})
                                else:
                                    # 检查是否有思考过程
                                    reasoning_content = message.get("reasoning_content", None)
                                    if reasoning_content:
                                        # 先添加思考过程
                                        self.chat_history.append({"role": "assistant", "content": f'🤔 思考过程：\n{reasoning_content}'})
                                        # 再添加最终答案
                                        self.chat_history.append({"role": "assistant", "content": f'💡 最终答案：\n{content}'})
                                    else:
                                        self.chat_history.append({"role": "assistant", "content": content or "无响应"})
                            else:
                                error_msg = f"API调用失败 (状态码: {response.status_code})"
                                self.chat_history.append({"role": "assistant", "content": error_msg})
                        
                        elif model_key == "deepseek":
                            headers = {"Authorization": f"Bearer {current_api_key}"}
                            payload = {
                                "model": submodel,
                                "messages": [
                                    {"role": "system", "content": "你好呀！我是你的智能助手，由深度求索（DeepSeek）研发，名字叫DeepSeek-R1。你可以把我当作一个随时在线、知识丰富又乐于助人的小伙伴～\n\n我能帮你做很多事情，比如：\n解答学习问题（语文、数学、英语、编程、历史……统统不在话下）\n写作助手（写作文、改简历、润色邮件、写小说、写剧本……我都能帮上忙）\n工作支持（做PPT、分析数据、写报告、制定计划……职场小能手就是我）\n生活百科（查天气、推荐美食、旅行攻略、健康小贴士……生活百事通）\n还有聊天解闷、讲笑话、陪你唠嗑\n\n无论你是学生、上班族、创作者，还是好奇宝宝，我都在这儿等你来撩～"},
                                    {"role": "user", "content": message}
                                ],
                                "temperature": temp,
                                "max_tokens": 2000
                            }

                            # 如果有MCP工具，添加到请求中
                            if tools:
                                payload["tools"] = tools
                                payload["tool_choice"] = "auto"
                            response = requests.post(model_info["base_url"], json=payload, headers=headers, timeout=120)
                            
                            if response.status_code == 200:
                                result = response.json()
                                message = result.get("choices", [{}])[0].get("message", {})
                                content = message.get("content", "")
                                tool_calls = message.get("tool_calls", [])

                                # 处理工具调用
                                if tool_calls and self.mcp_client and self.mcp_client.is_connected():
                                    tool_results = []
                                    for tool_call in tool_calls:
                                        function = tool_call.get("function", {})
                                        tool_name = function.get("name", "")
                                        arguments = json.loads(function.get("arguments", "{}"))

                                        try:
                                            tool_result = asyncio.run(self.mcp_client.call_tool(tool_name, arguments))
                                            tool_results.append(f"🔧 调用工具 {tool_name}: {tool_result}")
                                        except Exception as e:
                                            tool_results.append(f"❌ 工具调用失败 {tool_name}: {str(e)}")

                                    # 组合内容和工具结果
                                    if content:
                                        final_content = content + "\n\n" + "\n".join(tool_results)
                                    else:
                                        final_content = "\n".join(tool_results)

                                    self.chat_history.append({"role": "assistant", "content": final_content})
                                else:
                                    # 检查是否有思考过程
                                    reasoning_content = message.get("reasoning_content", None)
                                    if reasoning_content:
                                        # 先添加思考过程
                                        self.chat_history.append({"role": "assistant", "content": f'🤔 思考过程：\n{reasoning_content}'})
                                        # 再添加最终答案
                                        self.chat_history.append({"role": "assistant", "content": f'💡 最终答案：\n{content}'})
                                    else:
                                        self.chat_history.append({"role": "assistant", "content": content or "无响应"})
                            else:
                                error_msg = f"API调用失败 (状态码: {response.status_code})"
                                self.chat_history.append({"role": "assistant", "content": error_msg})
                        
                        elif model_key == "qwen":
                            # 使用OpenAI兼容客户端调用通义千问模型
                            try:
                                client = OpenAI(
                                    api_key=current_api_key,
                                    base_url=model_info["base_url"]
                                )
                                
                                # 构建消息列表
                                messages = [
                                    {"role": "system", "content": "你好呀！我是你的智能助手，由深度求索（DeepSeek）研发，名字叫DeepSeek-R1。你可以把我当作一个随时在线、知识丰富又乐于助人的小伙伴～\n\n我能帮你做很多事情，比如：\n解答学习问题（语文、数学、英语、编程、历史……统统不在话下）\n写作助手（写作文、改简历、润色邮件、写小说、写剧本……我都能帮上忙）\n工作支持（做PPT、分析数据、写报告、制定计划……职场小能手就是我）\n生活百科（查天气、推荐美食、旅行攻略、健康小贴士……生活百事通）\n还有聊天解闷、讲笑话、陪你唠嗑\n\n无论你是学生、上班族、创作者，还是好奇宝宝，我都在这儿等你来撩～"},
                                    {"role": "user", "content": message}
                                ]
                                
                                # 调用API
                                completion_args = {
                                    "model": submodel,
                                    "messages": messages,
                                    "temperature": temp,
                                    "max_tokens": 2000
                                }

                                # 如果有MCP工具，添加到请求中
                                if tools:
                                    completion_args["tools"] = tools
                                    completion_args["tool_choice"] = "auto"

                                completion = client.chat.completions.create(**completion_args)
                                
                                # 提取响应内容
                                message = completion.choices[0].message
                                content = message.content
                                tool_calls = getattr(message, 'tool_calls', None)

                                # 处理工具调用
                                if tool_calls and self.mcp_client and self.mcp_client.is_connected():
                                    tool_results = []
                                    for tool_call in tool_calls:
                                        function = tool_call.function
                                        tool_name = function.name
                                        arguments = json.loads(function.arguments)

                                        try:
                                            tool_result = asyncio.run(self.mcp_client.call_tool(tool_name, arguments))
                                            tool_results.append(f"🔧 调用工具 {tool_name}: {tool_result}")
                                        except Exception as e:
                                            tool_results.append(f"❌ 工具调用失败 {tool_name}: {str(e)}")

                                    # 组合内容和工具结果
                                    if content:
                                        final_content = content + "\n\n" + "\n".join(tool_results)
                                    else:
                                        final_content = "\n".join(tool_results)

                                    self.chat_history.append({'role': 'assistant', 'content': final_content})
                                else:
                                    # 检查是否有思考过程
                                    reasoning_content = getattr(message, 'reasoning_content', None)
                                    if reasoning_content:
                                        # 先添加思考过程
                                        self.chat_history.append({'role': 'assistant', 'content': f'🤔 思考过程：\n{reasoning_content}'})
                                        # 再添加最终答案
                                        self.chat_history.append({'role': 'assistant', 'content': f'💡 最终答案：\n{content}'})
                                    else:
                                        self.chat_history.append({'role': 'assistant', 'content': content or "无响应"})
                            except Exception as e:
                                import traceback
                                error_msg = f"API调用失败: {str(e)}"
                                print(f"大模型调用详细错误:\n{traceback.format_exc()}")
                                self.chat_history.append({"role": "assistant", "content": error_msg})

                        else:
                            # 通用OpenAI兼容API处理（包括OpenAI、其他兼容模型等）
                            try:
                                client = OpenAI(
                                    api_key=current_api_key,
                                    base_url=model_info["base_url"]
                                )

                                # 构建消息列表
                                messages = [
                                    {"role": "system", "content": "你好呀！我是你的智能助手，由深度求索（DeepSeek）研发，名字叫DeepSeek-R1。你可以把我当作一个随时在线、知识丰富又乐于助人的小伙伴～\n\n我能帮你做很多事情，比如：\n解答学习问题（语文、数学、英语、编程、历史……统统不在话下）\n写作助手（写作文、改简历、润色邮件、写小说、写剧本……我都能帮上忙）\n工作支持（做PPT、分析数据、写报告、制定计划……职场小能手就是我）\n生活百科（查天气、推荐美食、旅行攻略、健康小贴士……生活百事通）\n还有聊天解闷、讲笑话、陪你唠嗑\n\n无论你是学生、上班族、创作者，还是好奇宝宝，我都在这儿等你来撩～"},
                                    {"role": "user", "content": message}
                                ]

                                # 调用API
                                completion_args = {
                                    "model": submodel,
                                    "messages": messages,
                                    "temperature": temp,
                                    "max_tokens": 2000
                                }

                                # 如果有MCP工具，添加到请求中
                                if tools:
                                    completion_args["tools"] = tools
                                    completion_args["tool_choice"] = "auto"

                                completion = client.chat.completions.create(**completion_args)

                                # 提取响应内容
                                message = completion.choices[0].message
                                content = message.content
                                tool_calls = getattr(message, 'tool_calls', None)

                                # 处理工具调用
                                if tool_calls and self.mcp_client and self.mcp_client.is_connected():
                                    tool_results = []
                                    for tool_call in tool_calls:
                                        function = tool_call.function
                                        tool_name = function.name
                                        arguments = json.loads(function.arguments)

                                        try:
                                            tool_result = asyncio.run(self.mcp_client.call_tool(tool_name, arguments))
                                            tool_results.append(f"🔧 调用工具 {tool_name}: {tool_result}")
                                        except Exception as e:
                                            tool_results.append(f"❌ 工具调用失败 {tool_name}: {str(e)}")

                                    # 组合内容和工具结果
                                    if content:
                                        final_content = content + "\n\n" + "\n".join(tool_results)
                                    else:
                                        final_content = "\n".join(tool_results)

                                    self.chat_history.append({'role': 'assistant', 'content': final_content})
                                else:
                                    # 检查是否有思考过程
                                    reasoning_content = getattr(message, 'reasoning_content', None)
                                    if reasoning_content:
                                        # 先添加思考过程
                                        self.chat_history.append({'role': 'assistant', 'content': f'🤔 思考过程：\n{reasoning_content}'})
                                        # 再添加最终答案
                                        self.chat_history.append({'role': 'assistant', 'content': f'💡 最终答案：\n{content}'})
                                    else:
                                        self.chat_history.append({'role': 'assistant', 'content': content or "无响应"})
                            except Exception as e:
                                import traceback
                                error_msg = f"API调用失败: {str(e)}"
                                print(f"大模型调用详细错误:\n{traceback.format_exc()}")
                                self.chat_history.append({"role": "assistant", "content": error_msg})
                    
                    yield "", self.chat_history
                    
                except Exception as e:
                    self.chat_history.append({"role": "assistant", "content": f"错误: {str(e)}"})
                    yield "", self.chat_history
            
            # 绑定发送按钮
            send_btn.click(chat,
                        inputs=[msg, model_choice, submodel_choice, api_key_input, temperature, custom_prompt, use_mcp, chatbot],
                        outputs=[msg, chatbot]
            )

            # 绑定回车键
            msg.submit(
                chat,
                inputs=[msg, model_choice, submodel_choice, api_key_input, temperature, custom_prompt, use_mcp, chatbot],
                outputs=[msg, chatbot]
            )
        
        # 注册清理函数
        import atexit
        atexit.register(self.cleanup)
        return app

    # 资源清理方法
    def cleanup(self):
        if self.mcp_client:
            try:
                asyncio.run(self.mcp_client.cleanup())
                print("✅ MCP客户端资源已清理")
            except Exception as e:
                print(f"❌ 清理MCP客户端资源时出错: {str(e)}")

# 创建并启动应用
if __name__ == "__main__":
    try:
        print("🚀 启动多模型AI助手...")
        app = MultiModelChatApp()
        print("✅ 应用实例创建成功")
        interface = app.create_interface()
        # 注册退出处理器
        def cleanup_on_exit():
            """应用退出时的清理函数"""
            print("🧹 应用退出，清理MCP资源...")
            if hasattr(app, 'mcp_client') and app.mcp_client:
                try:
                    # 使用新的事件循环进行清理
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        loop.run_until_complete(app.mcp_client.cleanup())
                        print("✅ 退出时MCP资源清理完成")
                    finally:
                        loop.close()
                except Exception as e:
                    print(f"⚠️ 退出时清理警告: {str(e)}")

        # 注册退出处理器
        import atexit
        atexit.register(cleanup_on_exit)

        print("✅ 界面创建成功")
        print("🌐 启动Web界面...")
        interface.launch(
            server_name="127.0.0.1",
            server_port=7862,
            share=False,
            inbrowser=False  # 暂时不自动打开浏览器
        )
    except Exception as e:
        print(f"❌ 启动应用时出错: {str(e)}")
        import traceback
        traceback.print_exc()
