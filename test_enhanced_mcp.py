#!/usr/bin/env python3
"""
增强的MCP工具测试脚本
"""

import asyncio
import json
import requests
from typing import Optional, Dict, Any

class EnhancedMCPClient:
    def __init__(self, mcp_config: Dict[str, Any]):
        self.mcp_config = mcp_config
        self.server_type = None
        self.server_name = None
        self.sse_url = None
        self._connected = False

    async def connect_to_server(self, server_name: str):
        self.server_name = server_name
        server_config = self.mcp_config['mcpServers'][server_name]
        
        if 'type' in server_config and server_config['type'] == 'sse':
            self.server_type = 'sse'
            self.sse_url = server_config['url']
            
            print(f"🔗 正在连接到SSE服务器: {server_config['url']}")
            self._connected = True
            print(f"✅ 已连接到SSE服务器: {server_config['url']}")
            
            # 立即尝试获取工具列表
            tools = await self.get_available_tools()
            if tools:
                print(f"🔧 成功获取到 {len(tools)} 个工具")
            else:
                print("⚠️ 未能获取到工具列表，但连接已建立")

    def is_connected(self) -> bool:
        return self._connected and self.sse_url is not None

    async def _probe_sse_endpoints(self, sse_url):
        """探测SSE服务器的可用端点"""
        print(f"🔍 开始探测SSE端点...")
        
        # 可能的端点路径
        probe_paths = [
            '',  # 根路径
            '/tools',
            '/api/tools', 
            '/mcp/tools',
            '/list',
            '/api/list',
            '/mcp/list',
            '/capabilities',
            '/api/capabilities',
            '/health',
            '/status',
        ]
        
        available_endpoints = []
        base_url = sse_url.replace('/sse', '')
        
        for path in probe_paths:
            endpoint = base_url + path
            try:
                print(f"  🔍 探测: {endpoint}")
                response = requests.get(endpoint, timeout=5)
                print(f"    状态码: {response.status_code}")
                
                if response.status_code in [200, 404, 405]:
                    available_endpoints.append(endpoint)
                    print(f"    ✅ 端点可用: {endpoint}")
                    
                    # 如果是200，尝试解析响应
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"    📋 响应数据: {data}")
                        except:
                            print(f"    📄 文本响应: {response.text[:100]}")
                            
            except Exception as e:
                print(f"    ❌ 探测失败: {str(e)}")
                
        print(f"🎯 探测完成，发现 {len(available_endpoints)} 个可用端点")
        return available_endpoints

    def _parse_tools_response(self, result):
        """解析工具响应，提取工具数据"""
        tools_data = None
        
        print(f"🔍 解析响应数据: {type(result)} - {result}")
        
        if isinstance(result, dict):
            # JSON-RPC格式: {"result": {"tools": [...]}}
            if 'result' in result and isinstance(result['result'], dict) and 'tools' in result['result']:
                tools_data = result['result']['tools']
                print(f"  📋 找到JSON-RPC格式工具: {len(tools_data)} 个")
            # 直接格式: {"tools": [...]}
            elif 'tools' in result:
                tools_data = result['tools']
                print(f"  📋 找到直接格式工具: {len(tools_data)} 个")
            # 检查是否整个响应就是工具列表
            elif 'name' in result and 'description' in result:
                tools_data = [result]  # 单个工具
                print(f"  📋 找到单个工具")
        elif isinstance(result, list):
            tools_data = result
            print(f"  📋 找到数组格式工具: {len(tools_data)} 个")
            
        return tools_data

    def _format_tools(self, tools_data):
        """格式化工具数据为标准格式"""
        tools = []
        for tool in tools_data:
            if isinstance(tool, dict):
                tool_def = {
                    "type": "function",
                    "function": {
                        "name": tool.get("name", "unknown"),
                        "description": tool.get("description", "No description"),
                        "parameters": tool.get("inputSchema", tool.get("parameters", {
                            "type": "object",
                            "properties": {},
                            "required": []
                        }))
                    }
                }
                tools.append(tool_def)
                print(f"  🔧 格式化工具: {tool_def['function']['name']}")
        return tools

    async def get_available_tools(self):
        """Get list of available MCP tools"""
        if not self.is_connected():
            print("❌ MCP客户端未连接")
            return []

        if self.server_type == 'sse':
            try:
                sse_url = self.sse_url
                print(f"🔍 尝试从SSE服务器获取工具列表: {sse_url}")
                
                # 首先探测可用端点
                available_endpoints = await self._probe_sse_endpoints(sse_url)
                
                # 使用探测到的端点
                if available_endpoints:
                    endpoints_to_try = available_endpoints
                else:
                    endpoints_to_try = [
                        sse_url.replace('/sse', '/tools'),
                        sse_url.replace('/sse', ''),
                        sse_url + '/tools',
                    ]

                # 尝试不同的请求格式
                request_formats = [
                    {"jsonrpc": "2.0", "id": 1, "method": "tools/list", "params": {}},
                    {"method": "tools/list"},
                    {"method": "list_tools"},
                    {}
                ]

                for endpoint in endpoints_to_try:
                    print(f"\n🔗 测试端点: {endpoint}")
                    
                    # 尝试GET请求
                    try:
                        response = requests.get(endpoint, timeout=10)
                        print(f"📡 GET响应状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            try:
                                result = response.json()
                                tools_data = self._parse_tools_response(result)
                                if tools_data:
                                    tools = self._format_tools(tools_data)
                                    if tools:
                                        tool_names = [tool['function']['name'] for tool in tools]
                                        print(f"✅ GET成功获取工具: {tool_names}")
                                        return tools
                            except Exception as e:
                                print(f"❌ GET处理失败: {str(e)}")
                    except Exception as e:
                        print(f"❌ GET请求失败: {str(e)}")
                    
                    # 尝试POST请求
                    for request_data in request_formats:
                        try:
                            print(f"  📤 POST请求: {request_data}")
                            response = requests.post(
                                endpoint, 
                                json=request_data, 
                                headers={'Content-Type': 'application/json'},
                                timeout=10
                            )
                            
                            print(f"  📡 POST响应状态码: {response.status_code}")
                            
                            if response.status_code == 200:
                                try:
                                    result = response.json()
                                    tools_data = self._parse_tools_response(result)
                                    if tools_data:
                                        tools = self._format_tools(tools_data)
                                        if tools:
                                            tool_names = [tool['function']['name'] for tool in tools]
                                            print(f"✅ POST成功获取工具: {tool_names}")
                                            return tools
                                except Exception as e:
                                    print(f"  ❌ POST处理失败: {str(e)}")
                        except Exception as e:
                            print(f"  ❌ POST请求失败: {str(e)}")

                print("⚠️ 所有端点都未能获取到工具，返回默认工具")
                return [{
                    "type": "function",
                    "function": {
                        "name": "fetch_url",
                        "description": "Fetch content from a URL",
                        "parameters": {
                            "type": "object",
                            "properties": {"url": {"type": "string", "description": "The URL to fetch"}},
                            "required": ["url"]
                        }
                    }
                }]
                
            except Exception as e:
                print(f"❌ 获取SSE工具时发生错误: {str(e)}")
                return []

async def test_enhanced_sse():
    """测试增强的SSE连接"""
    print("🚀 开始增强的SSE测试...")
    
    mcp_config = {
        "mcpServers": {
            "fetch": {
                "type": "sse",
                "url": "https://mcp.api-inference.modelscope.net/9c06aa1527954a/sse"
            }
        }
    }
    
    client = EnhancedMCPClient(mcp_config)
    
    try:
        await client.connect_to_server("fetch")
        
        if client.is_connected():
            print("\n🎉 连接成功！")
            
            tools = await client.get_available_tools()
            
            if tools:
                print(f"\n✅ 最终获取到 {len(tools)} 个工具:")
                for i, tool in enumerate(tools, 1):
                    tool_info = tool['function']
                    print(f"  {i}. {tool_info['name']}: {tool_info['description']}")
            else:
                print("\n❌ 未获取到任何工具")
        else:
            print("\n❌ 连接失败")
            
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_enhanced_sse())
